"""
Psychiatry EMR - Security Monitoring System
Real-time security monitoring with failed access detection and bulk data access monitoring.
"""

import logging
import smtplib
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from email.mime.text import MimeText
from email.mime.multipart import <PERSON>me<PERSON><PERSON><PERSON><PERSON>
from sqlmodel import Session, select, func
from dataclasses import dataclass

from models.audit import AuditLog
from models.user import User
from services.database import get_db_session
from config.settings import get_settings

logger = logging.getLogger(__name__)

@dataclass
class SecurityAlert:
    """Security alert data structure"""
    alert_type: str
    severity: str  # LOW, MEDIUM, HIGH, CRITICAL
    user_id: Optional[int]
    username: Optional[str]
    description: str
    details: Dict[str, Any]
    timestamp: datetime
    source_ip: Optional[str] = None
    user_agent: Optional[str] = None

class SecurityMonitor:
    """Real-time security monitoring system"""
    
    def __init__(self, db_session: Session):
        self.db = db_session
        self.settings = get_settings()
        self.alerts: List[SecurityAlert] = []
        
        # Thresholds for security monitoring
        self.FAILED_LOGIN_THRESHOLD = 5  # Failed logins in time window
        self.FAILED_LOGIN_WINDOW = timedelta(minutes=15)
        self.BULK_ACCESS_THRESHOLD = 20  # Patient records accessed in time window
        self.BULK_ACCESS_WINDOW = timedelta(minutes=10)
        self.SUSPICIOUS_ACTIVITY_THRESHOLD = 50  # Total actions in time window
        self.SUSPICIOUS_ACTIVITY_WINDOW = timedelta(minutes=5)
        
    def monitor_failed_logins(self) -> List[SecurityAlert]:
        """Monitor for excessive failed login attempts"""
        alerts = []
        cutoff_time = datetime.utcnow() - self.FAILED_LOGIN_WINDOW
        
        # Query failed login attempts
        failed_logins = self.db.exec(
            select(AuditLog.user_id, func.count(AuditLog.id).label('count'))
            .where(
                AuditLog.action == "LOGIN_FAILED",
                AuditLog.timestamp >= cutoff_time,
                AuditLog.success == False
            )
            .group_by(AuditLog.user_id)
            .having(func.count(AuditLog.id) >= self.FAILED_LOGIN_THRESHOLD)
        ).all()
        
        for user_id, count in failed_logins:
            # Get user details
            user = self.db.exec(select(User).where(User.id == user_id)).first()
            username = user.username if user else f"Unknown (ID: {user_id})"
            
            alert = SecurityAlert(
                alert_type="FAILED_LOGIN_ATTEMPTS",
                severity="HIGH",
                user_id=user_id,
                username=username,
                description=f"Excessive failed login attempts detected",
                details={
                    "failed_attempts": count,
                    "time_window_minutes": self.FAILED_LOGIN_WINDOW.total_seconds() / 60,
                    "threshold": self.FAILED_LOGIN_THRESHOLD
                },
                timestamp=datetime.utcnow()
            )
            alerts.append(alert)
            
        return alerts
    
    def monitor_bulk_data_access(self) -> List[SecurityAlert]:
        """Monitor for bulk patient data access"""
        alerts = []
        cutoff_time = datetime.utcnow() - self.BULK_ACCESS_WINDOW
        
        # Query bulk patient access
        bulk_access = self.db.exec(
            select(AuditLog.user_id, func.count(AuditLog.id).label('count'))
            .where(
                AuditLog.action.in_(["READ", "READ"]),
                AuditLog.table_name == "patient",
                AuditLog.timestamp >= cutoff_time,
                AuditLog.success == True
            )
            .group_by(AuditLog.user_id)
            .having(func.count(AuditLog.id) >= self.BULK_ACCESS_THRESHOLD)
        ).all()
        
        for user_id, count in bulk_access:
            # Get user details
            user = self.db.exec(select(User).where(User.id == user_id)).first()
            username = user.username if user else f"Unknown (ID: {user_id})"
            
            alert = SecurityAlert(
                alert_type="BULK_DATA_ACCESS",
                severity="MEDIUM",
                user_id=user_id,
                username=username,
                description=f"Bulk patient data access detected",
                details={
                    "records_accessed": count,
                    "time_window_minutes": self.BULK_ACCESS_WINDOW.total_seconds() / 60,
                    "threshold": self.BULK_ACCESS_THRESHOLD
                },
                timestamp=datetime.utcnow()
            )
            alerts.append(alert)
            
        return alerts
    
    def monitor_suspicious_activity(self) -> List[SecurityAlert]:
        """Monitor for suspicious user activity patterns"""
        alerts = []
        cutoff_time = datetime.utcnow() - self.SUSPICIOUS_ACTIVITY_WINDOW
        
        # Query high-volume activity
        suspicious_activity = self.db.exec(
            select(AuditLog.user_id, func.count(AuditLog.id).label('count'))
            .where(
                AuditLog.timestamp >= cutoff_time
            )
            .group_by(AuditLog.user_id)
            .having(func.count(AuditLog.id) >= self.SUSPICIOUS_ACTIVITY_THRESHOLD)
        ).all()
        
        for user_id, count in suspicious_activity:
            # Get user details
            user = self.db.exec(select(User).where(User.id == user_id)).first()
            username = user.username if user else f"Unknown (ID: {user_id})"
            
            alert = SecurityAlert(
                alert_type="SUSPICIOUS_ACTIVITY",
                severity="MEDIUM",
                user_id=user_id,
                username=username,
                description=f"Suspicious high-volume activity detected",
                details={
                    "total_actions": count,
                    "time_window_minutes": self.SUSPICIOUS_ACTIVITY_WINDOW.total_seconds() / 60,
                    "threshold": self.SUSPICIOUS_ACTIVITY_THRESHOLD
                },
                timestamp=datetime.utcnow()
            )
            alerts.append(alert)
            
        return alerts
    
    def monitor_encryption_failures(self) -> List[SecurityAlert]:
        """Monitor for encryption/decryption failures"""
        alerts = []
        cutoff_time = datetime.utcnow() - timedelta(hours=1)
        
        # Query encryption failures
        encryption_failures = self.db.exec(
            select(AuditLog)
            .where(
                AuditLog.action == "DECRYPT_FAILED",
                AuditLog.timestamp >= cutoff_time,
                AuditLog.success == False
            )
        ).all()
        
        if len(encryption_failures) > 0:
            alert = SecurityAlert(
                alert_type="ENCRYPTION_FAILURES",
                severity="HIGH",
                user_id=None,
                username=None,
                description=f"Encryption/decryption failures detected",
                details={
                    "failure_count": len(encryption_failures),
                    "time_window_hours": 1,
                    "affected_tables": list(set(log.table_name for log in encryption_failures))
                },
                timestamp=datetime.utcnow()
            )
            alerts.append(alert)
            
        return alerts
    
    def monitor_unauthorized_access(self) -> List[SecurityAlert]:
        """Monitor for unauthorized access attempts"""
        alerts = []
        cutoff_time = datetime.utcnow() - timedelta(minutes=30)
        
        # Query unauthorized access attempts
        unauthorized_access = self.db.exec(
            select(AuditLog.user_id, func.count(AuditLog.id).label('count'))
            .where(
                AuditLog.timestamp >= cutoff_time,
                AuditLog.success == False,
                AuditLog.error_message.like("%Access denied%")
            )
            .group_by(AuditLog.user_id)
            .having(func.count(AuditLog.id) >= 3)
        ).all()
        
        for user_id, count in unauthorized_access:
            # Get user details
            user = self.db.exec(select(User).where(User.id == user_id)).first()
            username = user.username if user else f"Unknown (ID: {user_id})"
            
            alert = SecurityAlert(
                alert_type="UNAUTHORIZED_ACCESS",
                severity="HIGH",
                user_id=user_id,
                username=username,
                description=f"Multiple unauthorized access attempts detected",
                details={
                    "unauthorized_attempts": count,
                    "time_window_minutes": 30
                },
                timestamp=datetime.utcnow()
            )
            alerts.append(alert)
            
        return alerts
    
    def monitor_data_export_activity(self) -> List[SecurityAlert]:
        """Monitor for unusual data export activity"""
        alerts = []
        cutoff_time = datetime.utcnow() - timedelta(hours=24)
        
        # Query data export activities
        export_activities = self.db.exec(
            select(AuditLog.user_id, func.count(AuditLog.id).label('count'))
            .where(
                AuditLog.action.in_(["EXPORT", "DOWNLOAD", "BACKUP"]),
                AuditLog.timestamp >= cutoff_time,
                AuditLog.success == True
            )
            .group_by(AuditLog.user_id)
            .having(func.count(AuditLog.id) >= 5)
        ).all()
        
        for user_id, count in export_activities:
            # Get user details
            user = self.db.exec(select(User).where(User.id == user_id)).first()
            username = user.username if user else f"Unknown (ID: {user_id})"
            
            alert = SecurityAlert(
                alert_type="UNUSUAL_DATA_EXPORT",
                severity="MEDIUM",
                user_id=user_id,
                username=username,
                description=f"Unusual data export activity detected",
                details={
                    "export_count": count,
                    "time_window_hours": 24
                },
                timestamp=datetime.utcnow()
            )
            alerts.append(alert)
            
        return alerts
    
    def run_all_monitors(self) -> List[SecurityAlert]:
        """Run all security monitors and return combined alerts"""
        all_alerts = []
        
        try:
            all_alerts.extend(self.monitor_failed_logins())
            all_alerts.extend(self.monitor_bulk_data_access())
            all_alerts.extend(self.monitor_suspicious_activity())
            all_alerts.extend(self.monitor_encryption_failures())
            all_alerts.extend(self.monitor_unauthorized_access())
            all_alerts.extend(self.monitor_data_export_activity())
            
        except Exception as e:
            logger.error(f"Error running security monitors: {e}")
            
            # Create alert for monitoring system failure
            system_alert = SecurityAlert(
                alert_type="MONITORING_SYSTEM_ERROR",
                severity="CRITICAL",
                user_id=None,
                username=None,
                description="Security monitoring system encountered an error",
                details={"error": str(e)},
                timestamp=datetime.utcnow()
            )
            all_alerts.append(system_alert)
        
        self.alerts.extend(all_alerts)
        return all_alerts
    
    def send_alert_notification(self, alert: SecurityAlert) -> bool:
        """Send alert notification via email"""
        try:
            if not self.settings.smtp_server:
                logger.warning("SMTP not configured. Cannot send alert notifications.")
                return False
            
            # Create email message
            msg = MimeMultipart()
            msg['From'] = self.settings.smtp_username
            msg['To'] = self.settings.security_alert_email
            msg['Subject'] = f"[SECURITY ALERT] {alert.alert_type} - {alert.severity}"
            
            # Email body
            body = f"""
Security Alert Detected

Alert Type: {alert.alert_type}
Severity: {alert.severity}
Timestamp: {alert.timestamp}
User: {alert.username} (ID: {alert.user_id})
Description: {alert.description}

Details:
{self._format_alert_details(alert.details)}

This is an automated security alert from the Psychiatry EMR system.
Please investigate immediately if this represents unauthorized activity.
            """
            
            msg.attach(MimeText(body, 'plain'))
            
            # Send email
            with smtplib.SMTP(self.settings.smtp_server, self.settings.smtp_port) as server:
                if self.settings.smtp_use_tls:
                    server.starttls()
                if self.settings.smtp_username and self.settings.smtp_password:
                    server.login(self.settings.smtp_username, self.settings.smtp_password)
                server.send_message(msg)
            
            logger.info(f"Security alert notification sent: {alert.alert_type}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send security alert notification: {e}")
            return False
    
    def _format_alert_details(self, details: Dict[str, Any]) -> str:
        """Format alert details for email"""
        formatted = []
        for key, value in details.items():
            formatted.append(f"  {key}: {value}")
        return "\n".join(formatted)
    
    def log_alert(self, alert: SecurityAlert) -> None:
        """Log security alert to audit trail"""
        try:
            audit_log = AuditLog(
                user_id=alert.user_id,
                action="SECURITY_ALERT",
                table_name="security_monitor",
                success=True,
                new_values=f"Alert: {alert.alert_type}, Severity: {alert.severity}, Description: {alert.description}",
                timestamp=alert.timestamp
            )
            
            self.db.add(audit_log)
            self.db.commit()
            
        except Exception as e:
            logger.error(f"Failed to log security alert: {e}")
    
    def process_alerts(self, alerts: List[SecurityAlert]) -> None:
        """Process and handle security alerts"""
        for alert in alerts:
            # Log alert
            self.log_alert(alert)
            
            # Send notification for high/critical severity alerts
            if alert.severity in ["HIGH", "CRITICAL"]:
                self.send_alert_notification(alert)
            
            # Log to application logger
            log_level = {
                "LOW": logging.INFO,
                "MEDIUM": logging.WARNING,
                "HIGH": logging.ERROR,
                "CRITICAL": logging.CRITICAL
            }.get(alert.severity, logging.WARNING)
            
            logger.log(
                log_level,
                f"Security Alert: {alert.alert_type} - {alert.description} "
                f"(User: {alert.username}, Severity: {alert.severity})"
            )

def run_security_monitoring() -> None:
    """Main function to run security monitoring"""
    logger.info("Starting security monitoring scan...")
    
    try:
        with get_db_session() as db:
            monitor = SecurityMonitor(db)
            alerts = monitor.run_all_monitors()
            
            if alerts:
                logger.info(f"Found {len(alerts)} security alerts")
                monitor.process_alerts(alerts)
            else:
                logger.info("No security alerts detected")
                
    except Exception as e:
        logger.error(f"Security monitoring failed: {e}")

if __name__ == "__main__":
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Run security monitoring
    run_security_monitoring()
