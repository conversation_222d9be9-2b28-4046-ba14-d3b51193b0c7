[{"id": "TC001", "title": "user_login_functionality", "description": "Verify that the user login API endpoint securely authenticates users using the master password encryption and establishes a valid session with encrypted credentials."}, {"id": "TC002", "title": "patient_search_functionality", "description": "Test the patient search API endpoint to ensure it returns accurate patient records based on the search term with encrypted data handling and duplicate detection."}, {"id": "TC003", "title": "patient_creation_functionality", "description": "Validate the patient creation API endpoint to confirm new patient records are created with encrypted storage and duplicate detection alerts."}, {"id": "TC004", "title": "clinical_assessment_creation", "description": "Ensure the clinical assessment creation API endpoint correctly accepts DSM-5-TR based present illness data and stores it securely."}, {"id": "TC005", "title": "dsm5_disorders_retrieval", "description": "Test the API endpoint that retrieves available DSM-5 disorders to verify it returns the correct list of disorders from the configuration."}, {"id": "TC006", "title": "dsm5_criteria_retrieval", "description": "Verify the API endpoint for fetching criteria of a specific DSM-5 disorder returns accurate and complete diagnostic criteria."}, {"id": "TC007", "title": "dashboard_data_retrieval", "description": "Check that the dashboard API endpoint returns comprehensive patient overview data and quick access features as expected."}, {"id": "TC008", "title": "audit_logs_retrieval", "description": "Validate that the audit logs API endpoint returns a complete and accurate log of all user actions and data changes with proper timestamps and user attribution."}, {"id": "TC009", "title": "database_health_check", "description": "Test the database health check API endpoint to ensure it accurately reports the operational status and performance of the database."}, {"id": "TC010", "title": "security_status_check", "description": "Verify the security status check API endpoint returns current security metrics, encryption status, and alerts for any vulnerabilities."}]