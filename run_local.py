#!/usr/bin/env python3
"""
Local Development Runner for Psychiatry EMR
Runs the application locally without <PERSON><PERSON> for development and testing.
"""

import os
import sys
import subprocess
import time
import logging
from pathlib import Path

# Setup logging with UTF-8 encoding for Windows
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('psychiatry_emr.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def check_prerequisites():
    """Check if all prerequisites are installed"""
    logger.info("Checking prerequisites...")
    
    # Check Python version
    if sys.version_info < (3, 11):
        logger.error("Python 3.11 or higher is required")
        return False
    
    # Check if virtual environment is activated
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        logger.warning("Virtual environment not detected. Consider using a virtual environment.")
    
    # Check if requirements are installed
    try:
        import reflex
        import sqlmodel
        import psycopg2
        import cryptography
        import pydantic
        import <PERSON><PERSON><PERSON>ein  # Check for python-Levenshtein
        logger.info("✅ All required packages are installed")
        return True
    except ImportError as e:
        logger.error(f"Missing required package: {e}")
        logger.info("Install requirements with: pip install -r requirements.txt")
        return False

def setup_environment():
    """Setup environment variables for local development"""
    logger.info("Setting up environment...")
    
    # Set development environment variables
    os.environ.setdefault("DATABASE_URL", "sqlite:///psychiatry_emr_local.db")
    os.environ.setdefault("DEBUG_MODE", "true")
    os.environ.setdefault("LOG_LEVEL", "DEBUG")
    
    # Generate secure random values for development
    if not os.getenv("ENCRYPTION_SALT"):
        import secrets
        os.environ["ENCRYPTION_SALT"] = secrets.token_hex(32)
        logger.info("Generated secure ENCRYPTION_SALT for development")
    
    if not os.getenv("SECRET_KEY"):
        import secrets
        os.environ["SECRET_KEY"] = secrets.token_urlsafe(32)
        logger.info("Generated secure SECRET_KEY for development")
    
    os.environ.setdefault("SESSION_TIMEOUT_MINUTES", "60")
    os.environ.setdefault("MAX_LOGIN_ATTEMPTS", "10")
    os.environ.setdefault("LOCKOUT_DURATION_MINUTES", "5")
    
    # Set encoding for Windows
    os.environ.setdefault("PYTHONIOENCODING", "utf-8")
    os.environ.setdefault("PYTHONLEGACYWINDOWSFSENCODING", "utf-8")
    
    # Create logs directory
    Path("logs").mkdir(exist_ok=True)
    
    logger.info("✅ Environment setup completed")

def initialize_database():
    """Initialize the database"""
    logger.info("Initializing database...")
    
    try:
        # Import after environment is set
        from services.database import initialize_database
        initialize_database()
        logger.info("✅ Database initialized successfully")
        return True
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        return False

def run_application():
    """Run the Reflex application using reflex run command"""
    logger.info("Starting Psychiatry EMR application...")
    
    try:
        # Use reflex run command with correct port arguments
        subprocess.run([
            sys.executable, "-m", "reflex", "run",
            "--backend-port", "8000",
            "--frontend-port", "3000",
            "--backend-host", "127.0.0.1"
        ], check=True)
    except KeyboardInterrupt:
        logger.info("Application stopped by user")
    except subprocess.CalledProcessError as e:
        logger.error(f"Application failed to start: {e}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return False
    
    return True

def main():
    """Main function"""
    print("Psychiatry EMR - Local Development Runner")
    print("=" * 50)
    
    # Check prerequisites
    if not check_prerequisites():
        sys.exit(1)
    
    # Setup environment
    setup_environment()
    
    # Initialize database
    if not initialize_database():
        logger.error("Failed to initialize database")
        sys.exit(1)
    
    # Run application
    logger.info("Starting application on http://127.0.0.1:3000")
    logger.info("Press Ctrl+C to stop the application")
    
    if not run_application():
        sys.exit(1)

if __name__ == "__main__":
    main()
