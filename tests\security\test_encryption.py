# tests/security/test_encryption.py
import pytest
from security.encryption import EncryptionService, generate_salt

def test_encryption_roundtrip():
    """Test encryption and decryption work correctly"""
    salt = generate_salt()
    service = EncryptionService("test_password", salt)
    original = "Test Patient Name"
    
    encrypted = service.encrypt(original)
    assert encrypted != original
    assert len(encrypted) > 0
    
    decrypted = service.decrypt(encrypted)
    assert decrypted == original

def test_encryption_fails_gracefully():
    """Test encryption handles errors appropriately"""
    salt = generate_salt()
    service = EncryptionService("test_password", salt)
    
    # Test with invalid encrypted data
    with pytest.raises(Exception):
        service.decrypt("invalid_encrypted_data")

def test_different_passwords_different_encryption():
    """Test that different passwords produce different encrypted results"""
    salt = generate_salt()
    service1 = EncryptionService("password1", salt)
    service2 = EncryptionService("password2", salt)
    
    original = "Test Data"
    encrypted1 = service1.encrypt(original)
    encrypted2 = service2.encrypt(original)
    
    assert encrypted1 != encrypted2

def test_empty_string_encryption():
    """Test encryption of empty strings"""
    salt = generate_salt()
    service = EncryptionService("test_password", salt)
    
    encrypted = service.encrypt("")
    assert encrypted == ""
    
    decrypted = service.decrypt("")
    assert decrypted == ""

def test_large_data_encryption():
    """Test encryption of large data"""
    salt = generate_salt()
    service = EncryptionService("test_password", salt)
    
    # Create large test data (1KB)
    large_data = "A" * 1024
    
    encrypted = service.encrypt(large_data)
    assert encrypted != large_data
    
    decrypted = service.decrypt(encrypted)
    assert decrypted == large_data

def test_unicode_encryption():
    """Test encryption of unicode characters"""
    salt = generate_salt()
    service = EncryptionService("test_password", salt)
    
    unicode_data = "Test with émojis 🏥 and spëcial chars: ñáéíóú"
    
    encrypted = service.encrypt(unicode_data)
    assert encrypted != unicode_data
    
    decrypted = service.decrypt(encrypted)
    assert decrypted == unicode_data

def test_salt_generation():
    """Test salt generation produces unique values"""
    salt1 = generate_salt()
    salt2 = generate_salt()
    
    assert salt1 != salt2
    assert len(salt1) == 32  # 32 bytes
    assert len(salt2) == 32

def test_same_salt_same_key():
    """Test that same password and salt produce same encryption key"""
    salt = generate_salt()
    service1 = EncryptionService("test_password", salt)
    service2 = EncryptionService("test_password", salt)
    
    original = "Test Data"
    encrypted1 = service1.encrypt(original)
    
    # Service2 should be able to decrypt data encrypted by service1
    decrypted = service2.decrypt(encrypted1)
    assert decrypted == original

def test_encryption_deterministic_with_same_service():
    """Test that encryption is not deterministic (includes randomness)"""
    salt = generate_salt()
    service = EncryptionService("test_password", salt)
    
    original = "Test Data"
    encrypted1 = service.encrypt(original)
    encrypted2 = service.encrypt(original)
    
    # Should be different due to Fernet's built-in randomness
    assert encrypted1 != encrypted2
    
    # But both should decrypt to the same original
    assert service.decrypt(encrypted1) == original
    assert service.decrypt(encrypted2) == original
