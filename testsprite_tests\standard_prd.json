{"meta": {"project": "Psychiatry Patient Management System", "date": "2025-07-30", "prepared_by": "Generated by Development Manager"}, "product_overview": "A secure, local psychiatry electronic medical record (EMR) system developed using Python Reflex, integrating DSM-5-TR criteria for clinical assessments, comprehensive patient management capabilities, and robust multi-layer security features to ensure privacy and compliance with healthcare regulations.", "core_goals": ["Provide a secure, encrypted platform for managing psychiatric patient data locally.", "Implement DSM-5-TR diagnostic criteria to assist clinicians in structured assessments.", "Ensure comprehensive management of patient records including duplicate detection and encrypted search.", "Enable secure user authentication with master password encryption and session management.", "Maintain a detailed audit trail of all user actions and data changes for compliance and monitoring.", "Optimize database performance and support seamless development workflow with migrations and schema management.", "Deliver a responsive dashboard offering quick access to key features and patient overviews."], "key_features": ["Secure Authentication System with master password encryption and session management to ensure only authorized access.", "Comprehensive Patient Management including creation, search, and encrypted storage of patient records, with duplicate detection.", "DSM-5-TR integrated Clinical Assessment engine providing intelligent dynamic forms based on diagnostic criteria.", "Dashboard providing patient overview and quick navigation to critical application features.", "Multi-layer Security & Encryption using AES-256-GCM for PII data, bcrypt password hashing, and PostgreSQL pgcrypto as a backup.", "Comprehensive Audit System logging all user activities and data modifications to maintain accountability and compliance.", "Database Management with connection pooling, migrations via Alembic, and optimization with performance indexes.", "Role-Based Access Control (RBAC) for granular permission management according to user roles.", "Health Monitoring endpoints and security status checks to ensure operational integrity and security."], "user_flow_summary": ["User logs in via the secure authentication system using username and master password, establishing a session with encrypted credentials.", "From the dashboard, user can search existing patients or create new patient records through encrypted intelligent forms, with real-time duplicate detection alerts.", "Users perform clinical assessments using DSM-5-TR criteria-driven dynamic forms, entering structured present illness data and diagnoses.", "All user actions, including logins, data creations, updates, and deletions, are logged by the audit system for traceability.", "Database health and security status can be monitored through dedicated API endpoints ensuring transparency and proactive maintenance.", "Users manage their workflow through the Reflex front-end with immediate feedback, validation by Pydantic models, and navigation through patient management and clinical assessment modules."], "validation_criteria": ["All patient and clinical data models pass validation through Pydantic schema enforcement.", "Authentication process securely hashes and encrypts credentials; session management prevents unauthorized access.", "Audit logs accurately record all user actions with timestamps and user attribution; logs are retrievable via API.", "DSM-5-TR assessment forms dynamically reflect diagnostic criteria from configuration YAML and update correctly based on user input.", "Database migration scripts apply without errors and maintain data integrity; performance indexes improve query response times.", "Security auditing confirms strong AES-256 encryption, bcrypt password hashing, and resistance to SQL injection attacks.", "End-to-end tests demonstrate key user flows work as intended with 100% pass rate except known minor cosmetic warnings not affecting functionality.", "Application performance metrics meet target thresholds: query times <0.001s, encryption operations <0.001s, memory usage below specified limits.", "Role-based access restrictions enforce permission boundaries correctly across different user roles as tested in integration tests."], "code_summary": {"tech_stack": ["Python", "Reflex", "SQLModel", "PostgreSQL", "SQLite", "Pydantic", "FastAPI", "<PERSON><PERSON><PERSON>", "Alembic", "Cryptography", "PyYAML", "<PERSON><PERSON>", "Redis", "BCrypt"], "features": [{"name": "Authentication System", "description": "Secure user authentication with master password encryption and session management", "files": ["states/auth_state.py", "security/encryption.py", "main.py"], "api_doc": {"openapi": "3.0.0", "info": {"title": "Authentication API", "version": "1.0.0"}, "paths": {"/login": {"post": {"summary": "User login", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"username": {"type": "string"}, "password": {"type": "string"}}}}}}}}}}}, {"name": "Patient Management", "description": "Comprehensive patient record management with encryption and duplicate detection", "files": ["models/patient.py", "services/patient_service.py", "states/patient_state.py", "pages/patient_search.py", "components/patient_form.py"], "api_doc": {"openapi": "3.0.0", "info": {"title": "Patient Management API", "version": "1.0.0"}, "paths": {"/patients": {"get": {"summary": "Search patients", "parameters": [{"name": "search_term", "in": "query", "schema": {"type": "string"}}]}, "post": {"summary": "Create new patient", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PatientData"}}}}}}}, "components": {"schemas": {"PatientData": {"type": "object", "properties": {"name": {"type": "string"}, "dob": {"type": "string", "format": "date"}, "address": {"type": "string"}, "phone": {"type": "string"}, "email": {"type": "string"}, "education": {"type": "string"}, "occupation": {"type": "string"}, "living_situation": {"type": "string"}}}}}}}, {"name": "Clinical Assessment", "description": "DSM-5-TR integrated clinical assessments with intelligent forms", "files": ["models/clinical.py", "services/clinical_service.py", "services/dsm5_engine.py", "states/clinical_state.py", "pages/clinical_assessment.py", "components/dsm5_forms.py", "config/dsm5_criteria.yaml"], "api_doc": {"openapi": "3.0.0", "info": {"title": "Clinical Assessment API", "version": "1.0.0"}, "paths": {"/assessments": {"post": {"summary": "Create clinical assessment", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PresentIllnessData"}}}}}}, "/dsm5/disorders": {"get": {"summary": "Get available DSM-5 disorders"}}, "/dsm5/criteria/{disorder}": {"get": {"summary": "Get criteria for specific disorder"}}}, "components": {"schemas": {"PresentIllnessData": {"type": "object", "properties": {"patient_id": {"type": "integer"}, "assessment_date": {"type": "string", "format": "date"}, "chief_complaint": {"type": "string"}, "history_present_illness": {"type": "string"}, "primary_diagnosis": {"type": "string"}, "secondary_diagnoses": {"type": "string"}, "treatment_plan": {"type": "string"}}}}}}}, {"name": "Dashboard", "description": "Main dashboard with patient overview and quick access to features", "files": ["pages/dashboard.py"], "api_doc": {"openapi": "3.0.0", "info": {"title": "Dashboard API", "version": "1.0.0"}, "paths": {"/dashboard": {"get": {"summary": "Get dashboard data"}}}}}, {"name": "Audit System", "description": "Comprehensive audit logging for all user actions and data changes", "files": ["models/audit.py", "services/audit_service.py"], "api_doc": {"openapi": "3.0.0", "info": {"title": "Audit API", "version": "1.0.0"}, "paths": {"/audit/logs": {"get": {"summary": "Get audit logs"}}}}}, {"name": "Database Management", "description": "Database connection, migration, and performance optimization", "files": ["services/database.py", "alembic/env.py", "sql/performance_indexes.sql", "sql/setup_security.sql"], "api_doc": {"openapi": "3.0.0", "info": {"title": "Database API", "version": "1.0.0"}, "paths": {"/health": {"get": {"summary": "Database health check"}}}}}, {"name": "Security & Encryption", "description": "Multi-layer encryption, master password authentication, and security monitoring", "files": ["security/encryption.py", "monitoring/security_monitor.py"], "api_doc": {"openapi": "3.0.0", "info": {"title": "Security API", "version": "1.0.0"}, "paths": {"/security/status": {"get": {"summary": "Security status check"}}}}}]}}