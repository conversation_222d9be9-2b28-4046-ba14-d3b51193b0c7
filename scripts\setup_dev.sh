#!/bin/bash
# setup_dev.sh - Development Environment Setup Script for Psychiatry EMR
# This script sets up a complete development environment

set -euo pipefail  # Exit on error, undefined vars, pipe failures

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running on supported OS
check_os() {
    log_info "Checking operating system..."
    
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        OS="linux"
        log_success "Linux detected"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
        log_success "macOS detected"
    elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
        OS="windows"
        log_success "Windows detected"
    else
        log_error "Unsupported operating system: $OSTYPE"
        exit 1
    fi
}

# Check Python version
check_python() {
    log_info "Checking Python version..."
    
    if ! command -v python3 &> /dev/null; then
        log_error "Python 3 is not installed. Please install Python 3.9 or higher."
        exit 1
    fi
    
    PYTHON_VERSION=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
    REQUIRED_VERSION="3.9"
    
    if python3 -c "import sys; exit(0 if sys.version_info >= (3, 9) else 1)"; then
        log_success "Python $PYTHON_VERSION detected (>= $REQUIRED_VERSION)"
    else
        log_error "Python $PYTHON_VERSION detected. Python $REQUIRED_VERSION or higher is required."
        exit 1
    fi
}

# Check if PostgreSQL is available
check_postgresql() {
    log_info "Checking PostgreSQL availability..."
    
    if command -v psql &> /dev/null; then
        log_success "PostgreSQL client found"
        POSTGRES_VERSION=$(psql --version | awk '{print $3}' | head -1)
        log_info "PostgreSQL version: $POSTGRES_VERSION"
    else
        log_warning "PostgreSQL client not found. You may need to install it for production use."
        log_info "For development, SQLite will be used instead."
    fi
}

# Create virtual environment
setup_venv() {
    log_info "Setting up Python virtual environment..."
    
    if [ ! -d ".venv" ]; then
        python3 -m venv .venv
        log_success "Virtual environment created"
    else
        log_info "Virtual environment already exists"
    fi
    
    # Activate virtual environment
    source .venv/bin/activate || source .venv/Scripts/activate
    log_success "Virtual environment activated"
    
    # Upgrade pip
    log_info "Upgrading pip..."
    pip install --upgrade pip
    log_success "pip upgraded"
}

# Install dependencies
install_dependencies() {
    log_info "Installing Python dependencies..."
    
    # Install production dependencies
    pip install -e .
    
    # Install development dependencies
    pip install -e ".[dev]"
    
    log_success "Dependencies installed"
}

# Setup pre-commit hooks
setup_precommit() {
    log_info "Setting up pre-commit hooks..."
    
    if command -v pre-commit &> /dev/null; then
        pre-commit install
        pre-commit install --hook-type commit-msg
        log_success "Pre-commit hooks installed"
    else
        log_warning "pre-commit not found. Installing..."
        pip install pre-commit
        pre-commit install
        pre-commit install --hook-type commit-msg
        log_success "Pre-commit installed and hooks set up"
    fi
}

# Create environment file
setup_env() {
    log_info "Setting up environment configuration..."
    
    if [ ! -f ".env" ]; then
        log_info "Creating .env file from template..."
        
        cat > .env << EOF
# Psychiatry EMR Development Environment Configuration
# DO NOT commit this file to version control

# Database Configuration
DATABASE_URL=sqlite:///psychiatry_emr_dev.db
# For PostgreSQL: postgresql://username:password@localhost:5432/psychiatry_emr_dev

# Security Configuration
ENCRYPTION_SALT=$(python3 -c "import secrets; print(secrets.token_hex(32))")
SECRET_KEY=$(python3 -c "import secrets; print(secrets.token_urlsafe(32))")

# Application Configuration
DEBUG_MODE=true
LOG_LEVEL=DEBUG
LOG_FILE=logs/psychiatry_emr.log

# Development Settings
TESTING=false
DEVELOPMENT=true

# Optional: Email Configuration (for notifications)
# SMTP_SERVER=localhost
# SMTP_PORT=587
# SMTP_USERNAME=
# SMTP_PASSWORD=
# SMTP_USE_TLS=true

# Optional: Backup Configuration
# BACKUP_DIRECTORY=backups/
# BACKUP_RETENTION_DAYS=30
EOF
        
        log_success ".env file created with secure random values"
        log_warning "Please review and update .env file as needed"
    else
        log_info ".env file already exists"
    fi
}

# Create necessary directories
create_directories() {
    log_info "Creating necessary directories..."
    
    directories=(
        "logs"
        "backups"
        "uploads"
        "temp"
        "htmlcov"
        ".pytest_cache"
    )
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            log_info "Created directory: $dir"
        fi
    done
    
    log_success "Directories created"
}

# Initialize database
setup_database() {
    log_info "Setting up development database..."
    
    # Source environment variables
    if [ -f ".env" ]; then
        export $(cat .env | grep -v '^#' | xargs)
    fi
    
    # Initialize database with Alembic
    if [ -f "alembic.ini" ]; then
        log_info "Running database migrations..."
        alembic upgrade head
        log_success "Database migrations completed"
    else
        log_warning "alembic.ini not found. Database migrations skipped."
    fi
    
    # Run database setup script if it exists
    if [ -f "scripts/init_db.py" ]; then
        log_info "Running database initialization script..."
        python scripts/init_db.py
        log_success "Database initialization completed"
    fi
}

# Run initial tests
run_tests() {
    log_info "Running initial test suite..."
    
    # Run quick tests to verify setup
    if pytest tests/ -x --tb=short -q; then
        log_success "Initial tests passed"
    else
        log_warning "Some tests failed. This might be expected in a new setup."
        log_info "Run 'scripts/run_tests.sh' for detailed test results"
    fi
}

# Setup IDE configuration
setup_ide() {
    log_info "Setting up IDE configuration..."
    
    # Create .vscode directory if it doesn't exist
    if [ ! -d ".vscode" ]; then
        mkdir -p .vscode
        
        # Create settings.json
        cat > .vscode/settings.json << EOF
{
    "python.defaultInterpreterPath": "./.venv/bin/python",
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": false,
    "python.linting.flake8Enabled": false,
    "python.linting.mypyEnabled": true,
    "python.formatting.provider": "black",
    "python.formatting.blackArgs": ["--line-length", "100"],
    "python.sortImports.args": ["--profile", "black"],
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.organizeImports": true
    },
    "files.exclude": {
        "**/__pycache__": true,
        "**/.pytest_cache": true,
        "**/.mypy_cache": true,
        "**/htmlcov": true
    }
}
EOF
        
        log_success "VS Code configuration created"
    fi
}

# Print setup summary
print_summary() {
    log_success "Development environment setup completed!"
    echo
    echo "Next steps:"
    echo "1. Activate virtual environment: source .venv/bin/activate"
    echo "2. Review and update .env file if needed"
    echo "3. Run tests: scripts/run_tests.sh"
    echo "4. Start development server: reflex run"
    echo "5. Check code quality: scripts/check_code_quality.sh"
    echo
    echo "Useful commands:"
    echo "- Run all tests: pytest"
    echo "- Run security tests: pytest -m security"
    echo "- Format code: black ."
    echo "- Lint code: ruff check ."
    echo "- Type check: mypy ."
    echo
    log_info "Happy coding! 🚀"
}

# Main setup function
main() {
    echo "🏥 Psychiatry EMR Development Environment Setup"
    echo "=============================================="
    echo
    
    check_os
    check_python
    check_postgresql
    setup_venv
    install_dependencies
    setup_precommit
    setup_env
    create_directories
    setup_database
    setup_ide
    run_tests
    print_summary
}

# Run main function
main "$@"
