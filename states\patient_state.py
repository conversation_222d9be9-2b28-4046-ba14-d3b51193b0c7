"""
Psychiatry EMR - Patient State Management
Enhanced state management with Pydantic data contracts and toast notifications.
"""

import reflex as rx
from typing import Optional, List, Dict, Any
import logging
from datetime import datetime

from models.patient import PatientData
from services.database import get_db_session_with_user
from services.patient_service import PatientService
from services.audit_service import AuditService

logger = logging.getLogger(__name__)

class PatientState(rx.State):
    """Patient management state with Pydantic data contracts and toast notifications"""
    
    # Use Pydantic model instead of dict
    current_patient: Optional[PatientData] = None
    search_term: str = ""
    search_results: List[PatientData] = []
    potential_duplicates: List[dict] = []
    
    # Pagination
    current_page: int = 1
    page_size: int = 25
    total_results: int = 0
    total_pages: int = 0
    
    # UI state
    is_loading: bool = False
    error_message: str = ""
    success_message: str = ""
    show_advanced_filters: bool = False  # For toast notifications
    
    @rx.var
    def current_user_id(self) -> int:
        """Get current user ID from JWT token"""
        if self.router.session.client_token:
            # Extract from JWT - implementation depends on Reflex auth structure
            return 1  # Placeholder
        return 0
    
    def show_success(self, message: str):
        """Show success toast notification"""
        self.success_message = message
        # Auto-clear after 3 seconds (would need timer in real implementation)
    
    def show_error(self, message: str):
        """Show error toast notification"""
        self.error_message = message
    
    def clear_messages(self):
        """Clear all notification messages"""
        self.error_message = ""
        self.success_message = ""

    def toggle_advanced_filters(self):
        """Toggle the advanced filters visibility"""
        self.show_advanced_filters = not self.show_advanced_filters
    
    def search_patients(self):
        """Search patients with pagination and proper error handling"""
        # Use the current search_term from the state
        self.current_page = 1  # Reset to first page on new search

        
        if not self.search_term.strip():
            self.search_results = []
            self.total_results = 0
            self.total_pages = 0
            return
        
        self.is_loading = True
        self.clear_messages()
        
        try:
            # Calculate offset for pagination
            offset = (self.current_page - 1) * self.page_size

            # Use patient service with proper database session
            with get_db_session_with_user(self.current_user_id) as db:
                audit_service = AuditService(db, self.current_user_id)
                patient_service = PatientService(db, audit_service, self.current_user_id)

                # Perform search
                results, total_count = patient_service.search_patients(
                    search_term=self.search_term,
                    limit=self.page_size,
                    offset=offset
                )

                self.search_results = results
                self.total_results = total_count
                self.total_pages = (total_count + self.page_size - 1) // self.page_size

                logger.info(f"Search completed: {len(results)} results found")
            
        except Exception as e:
            logger.error(f"Search failed: {e}")
            self.show_error("Search failed. Please try again.")
            self.search_results = []
            self.total_results = 0
            self.total_pages = 0
        finally:
            self.is_loading = False
    
    def load_patient(self, patient_id: int):
        """Load patient with comprehensive error handling"""
        self.is_loading = True
        self.clear_messages()
        
        try:
            # Use patient service with proper database session
            with get_db_session_with_user(self.current_user_id) as db:
                audit_service = AuditService(db, self.current_user_id)
                patient_service = PatientService(db, audit_service, self.current_user_id)

                # Load patient
                patient = patient_service.get_patient(patient_id)
                if patient:
                    self.current_patient = patient
                    self.show_success("Patient loaded successfully")
                    logger.info(f"Patient {patient_id} loaded successfully")
                else:
                    self.show_error("Patient not found or access denied")
                    logger.warning(f"Patient {patient_id} not found or access denied")
                
        except Exception as e:
            logger.error(f"Failed to load patient {patient_id}: {e}")
            self.show_error("Failed to load patient data")
        finally:
            self.is_loading = False
    
    def create_patient(self, patient_data: dict):
        """Create new patient with validation"""
        self.is_loading = True
        self.clear_messages()
        
        try:
            # Validate with Pydantic
            validated_data = PatientData(**patient_data)

            # Use patient service with proper database session
            with get_db_session_with_user(self.current_user_id) as db:
                audit_service = AuditService(db, self.current_user_id)
                patient_service = PatientService(db, audit_service, self.current_user_id)

                # Create patient
                new_patient = patient_service.create_patient(validated_data)
                if new_patient:
                    self.current_patient = new_patient
                    self.show_success("Patient created successfully")
                    logger.info(f"Patient created successfully: ID {new_patient.id}")
                else:
                    self.show_error("Failed to create patient")
                    logger.error("Patient creation failed")
            
        except ValueError as e:
            logger.warning(f"Validation error: {e}")
            self.show_error(f"Invalid data: {e}")
        except Exception as e:
            logger.error(f"Failed to create patient: {e}")
            self.show_error("Failed to create patient")
        finally:
            self.is_loading = False
    
    def check_duplicates(self, patient_data: dict):
        """Check for potential duplicate patients"""
        try:
            validated_data = PatientData(**patient_data)

            # Use patient service with proper database session
            with get_db_session_with_user(self.current_user_id) as db:
                audit_service = AuditService(db, self.current_user_id)
                patient_service = PatientService(db, audit_service, self.current_user_id)

                # Check for duplicates
                duplicates = patient_service.find_potential_duplicates(validated_data)
                self.potential_duplicates = [
                    {
                        "id": dup.id,
                        "name": f"{dup.first_name} {dup.last_name}",
                        "date_of_birth": dup.date_of_birth.isoformat() if dup.date_of_birth else "",
                        "phone": dup.phone_number or "",
                        "similarity_score": 0.85  # Placeholder - would be calculated
                    }
                    for dup in duplicates
                ]

                logger.info(f"Found {len(duplicates)} potential duplicates")
                
        except Exception as e:
            logger.error(f"Duplicate check failed: {e}")
            self.potential_duplicates = []
    
    def next_page(self):
        """Go to next page of search results"""
        if self.current_page < self.total_pages:
            self.search_patients(page=self.current_page + 1)
    
    def prev_page(self):
        """Go to previous page of search results"""
        if self.current_page > 1:
            self.search_patients(page=self.current_page - 1)
    
    def go_to_page(self, page: int):
        """Go to specific page"""
        if 1 <= page <= self.total_pages:
            self.search_patients(page=page)
