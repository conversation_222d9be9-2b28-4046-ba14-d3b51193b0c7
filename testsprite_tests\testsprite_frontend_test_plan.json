[{"id": "TC001", "title": "Successful User Login with Valid Credentials", "description": "Verify that a user can log in successfully with correct username and master password, establishing an active session.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Navigate to the login page."}, {"type": "action", "description": "Enter a valid username."}, {"type": "action", "description": "Enter the correct master password."}, {"type": "action", "description": "Click the login button."}, {"type": "assertion", "description": "Confirm login is successful and a secure session is established with encrypted credentials."}]}, {"id": "TC002", "title": "Failed Login with Incorrect Credentials", "description": "Ensure login fails when a wrong username or password is used and appropriate error messages are shown.", "category": "error handling", "priority": "High", "steps": [{"type": "action", "description": "Navigate to the login page."}, {"type": "action", "description": "Enter an invalid username or password."}, {"type": "action", "description": "Click the login button."}, {"type": "assertion", "description": "Verify login is denied and user is notified of invalid credentials without revealing details."}]}, {"id": "TC003", "title": "Patient Record Creation with <PERSON>id <PERSON>", "description": "Verify that a new patient record can be created successfully using the encrypted patient creation form with all valid fields.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Access the patient creation form from the dashboard."}, {"type": "action", "description": "Fill in all patient details with valid inputs including name, date of birth, address, contact info, education, occupation, and living situation."}, {"type": "action", "description": "Submit the patient creation form."}, {"type": "assertion", "description": "Confirm patient record is saved securely with AES-256-GCM encryption and is retrievable via encrypted search."}]}, {"id": "TC004", "title": "Duplicate Patient Detection Alert", "description": "Verify that the system identifies potential duplicate patient records during creation or search and alerts the user.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Input patient data that matches or closely resembles existing records."}, {"type": "action", "description": "Submit the patient creation or perform a search action."}, {"type": "assertion", "description": "Confirm that duplicate detection triggers and displays an appropriate alert to prevent duplication."}]}, {"id": "TC005", "title": "Search Existing Patients with Encrypted Data Retrieval", "description": "Verify that users can search existing patients using encrypted search parameters and receive correct decrypted patient data.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Go to the patient search page from the dashboard."}, {"type": "action", "description": "Enter a valid search term that matches encrypted patient data fields."}, {"type": "action", "description": "Submit the search request."}, {"type": "assertion", "description": "Verify that returned patient records are correct and decrypted properly for user viewing."}]}, {"id": "TC006", "title": "Create Clinical Assessment with DSM-5-TR Criteria", "description": "Verify the clinical assessment form dynamically changes according to DSM-5-TR diagnostic criteria and accepts valid structured input.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Navigate to clinical assessment module."}, {"type": "action", "description": "Select a disorder to assess, triggering DSM-5-TR criteria-based dynamic form fields."}, {"type": "action", "description": "Fill out the present illness history, chief complaint, primary and secondary diagnoses, and treatment plan with valid data."}, {"type": "action", "description": "Submit the clinical assessment."}, {"type": "assertion", "description": "Confirm the assessment is saved accurately and reflects the correct criteria and data validation."}]}, {"id": "TC007", "title": "Dynamic Update of DSM-5-TR Assessment Form Based on User Input", "description": "Ensure that the clinical assessment form adjusts displayed fields dynamically as the user inputs data and selects diagnostic options.", "category": "functional", "priority": "Medium", "steps": [{"type": "action", "description": "Open the clinical assessment form."}, {"type": "action", "description": "Select different disorders and criteria options."}, {"type": "assertion", "description": "Verify that form fields change dynamically according to the DSM-5-TR configuration YAML and current user input."}]}, {"id": "TC008", "title": "Audit Logging of User Actions and Data Changes", "description": "Verify that all critical user actions including logins, patient data creation, updates, deletions, and clinical assessments are logged with accurate timestamps and user attribution.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Perform a series of user actions: login, create patient, update patient, create clinical assessment, delete patient."}, {"type": "action", "description": "Query audit logs via the audit API."}, {"type": "assertion", "description": "Ensure all performed actions are accurately logged with correct details and timestamps."}]}, {"id": "TC009", "title": "Retrieve Audit Logs via API", "description": "Test that audit logs can be retrieved securely and efficiently using the designated API endpoint.", "category": "functional", "priority": "Medium", "steps": [{"type": "action", "description": "Send a GET request to the /audit/logs API endpoint."}, {"type": "assertion", "description": "Confirm response contains recent audit logs relevant to user actions and access is authorized per role permissions."}]}, {"id": "TC010", "title": "Database Health Check Endpoint Returns Expected Status", "description": "Verify that the database health check API endpoint returns success status and performance metrics reflecting operational health.", "category": "functional", "priority": "Medium", "steps": [{"type": "action", "description": "Send a GET request to the /health API endpoint."}, {"type": "assertion", "description": "Check that the response indicates database is healthy, connection pooling is active, and performance indexes are functioning."}]}, {"id": "TC011", "title": "Database Migration Scripts Apply Without Errors", "description": "Ensure that Alembic migration scripts execute successfully, maintaining data integrity and schema correctness.", "category": "error handling", "priority": "High", "steps": [{"type": "action", "description": "Execute alembic migration commands on test database."}, {"type": "assertion", "description": "Verify no errors occurred during migrations and the resulting database schema matches expected structure."}]}, {"id": "TC012", "title": "Role-Based Access Control Enforcement", "description": "Test that users are restricted or permitted access to various features according to their assigned roles.", "category": "security", "priority": "High", "steps": [{"type": "action", "description": "Log in as users with different roles."}, {"type": "action", "description": "Attempt to access restricted modules such as patient creation, clinical assessments, audit logs, and security status."}, {"type": "assertion", "description": "Confirm that access is granted only if the role permits and properly denied otherwise without exposing sensitive info."}]}, {"id": "TC013", "title": "AES-256-GCM Encryption of Patient PII Data", "description": "Verify that patient personally identifiable information is encrypted at rest using AES-256-GCM and cannot be retrieved in plaintext without authorization.", "category": "security", "priority": "High", "steps": [{"type": "action", "description": "Create or fetch a patient record."}, {"type": "assertion", "description": "Check the stored patient data in the database is encrypted and meets AES-256-GCM standards."}]}, {"id": "TC014", "title": "Bcrypt Password Hashing for Master Passwords", "description": "Ensure that user passwords are hashed securely using bcrypt and verify resistance to common hashing vulnerabilities.", "category": "security", "priority": "High", "steps": [{"type": "action", "description": "Register or update user password."}, {"type": "assertion", "description": "Verify password is stored with bcrypt hash and cannot be reversed or extracted."}]}, {"id": "TC015", "title": "Security Status Endpoint Reflects System Integrity", "description": "Check that the security monitoring API endpoint returns current encryption, authentication, and vulnerability status accurately.", "category": "security", "priority": "Medium", "steps": [{"type": "action", "description": "Send a GET request to the /security/status API endpoint."}, {"type": "assertion", "description": "Ensure response includes details on encryption strength, session management status, and detected security issues."}]}, {"id": "TC016", "title": "Application Performance Metrics Meet Targets", "description": "Verify that query execution times, encryption operations, and memory usage remain within specified limits under normal and peak loads.", "category": "performance", "priority": "High", "steps": [{"type": "action", "description": "Execute typical user workflows such as login, patient search, patient creation, and clinical assessment submissions."}, {"type": "assertion", "description": "Measure response times and resource usage, ensuring query times <0.001s, encryption <0.001s, and memory usage below defined thresholds."}]}, {"id": "TC017", "title": "Dashboard Loads with Patient Overview and Quick Navigation", "description": "Confirm that the dashboard loads rapidly and displays a summary overview of patients with accessible links to core features.", "category": "ui", "priority": "Medium", "steps": [{"type": "action", "description": "Log in and navigate to the dashboard page."}, {"type": "assertion", "description": "Verify that patient overview and navigation features load correctly and are responsive."}]}, {"id": "TC018", "title": "Pydantic Schema Validation for Patient and Clinical Data", "description": "Ensure that all patient and clinical data models conform strictly to Pydantic schema validation rules preventing invalid or malformed data.", "category": "error handling", "priority": "High", "steps": [{"type": "action", "description": "Attempt to create or update patient and clinical assessment records with invalid data such as missing required fields, wrong data types, or malformed formats."}, {"type": "assertion", "description": "Confirm that validation errors are thrown and the invalid data is rejected before persistence."}]}, {"id": "TC019", "title": "Session Management Enforces Secure Timeout and Re-Authentication", "description": "Verify that user sessions are managed securely with enforced timeouts, session renewal, and re-authentication requirements.", "category": "security", "priority": "High", "steps": [{"type": "action", "description": "Log in and remain idle beyond configured session timeout period."}, {"type": "assertion", "description": "Confirm user is logged out and required to re-authenticate to continue."}]}, {"id": "TC020", "title": "Resistance to SQL Injection Attacks on Patient Search and Login", "description": "Test that input fields for login and patient search APIs properly sanitize and escape input to prevent SQL injection.", "category": "security", "priority": "High", "steps": [{"type": "action", "description": "Send crafted SQL injection payloads as input in username, password, and patient search term parameters."}, {"type": "assertion", "description": "Verify no unauthorized data access or server errors occur and inputs are safely handled."}]}]