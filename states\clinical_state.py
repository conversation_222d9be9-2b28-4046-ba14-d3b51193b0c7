"""
Psychiatry EMR - Clinical State Management
Clinical assessment state with DSM-5-TR integration and real-time evaluation.
"""

import reflex as rx
from typing import Optional, List, Dict, Any
import logging
from datetime import date

from models.clinical import PresentIllnessData
from services.dsm5_engine import DSM5RuleEngine, DisorderCriteria
from services.database import get_db_session_with_user
from services.clinical_service import ClinicalService
from services.audit_service import AuditService

logger = logging.getLogger(__name__)

class ClinicalState(rx.State):
    """Clinical assessment state with DSM-5-TR integration"""
    
    # Current assessment
    current_assessment: Optional[PresentIllnessData] = None
    patient_assessments: List[PresentIllnessData] = []
    
    # DSM-5-TR state
    available_disorders: List[Dict[str, str]] = []
    selected_disorder: str = ""
    disorder_criteria: Optional[DisorderCriteria] = None
    criterion_responses: Dict[str, bool] = {}
    dsm5_evaluation: Optional[Dict[str, Any]] = None
    
    # Form state
    assessment_form: Dict[str, Any] = {
        "patient_id": 0,
        "assessment_date": "",
        "chief_complaint": "",
        "history_present_illness": "",
        "primary_diagnosis": "",
        "secondary_diagnoses": "",
        "treatment_plan": ""
    }
    
    # UI state
    is_loading: bool = False
    error_message: str = ""
    success_message: str = ""
    
    @rx.var
    def current_user_id(self) -> int:
        """Get current user ID from authentication"""
        return 1  # Placeholder
    
    def load_disorders(self):
        """Load available DSM-5-TR disorders"""
        try:
            engine = DSM5RuleEngine()
            self.available_disorders = engine.list_available_disorders()
            logger.info(f"Loaded {len(self.available_disorders)} disorders")
        except Exception as e:
            logger.error(f"Failed to load disorders: {e}")
            self.show_error("Failed to load DSM-5-TR criteria")
    
    def select_disorder(self, disorder_code: str):
        """Select disorder and load its criteria"""
        self.selected_disorder = disorder_code
        self.criterion_responses = {}
        self.dsm5_evaluation = None
        
        try:
            engine = DSM5RuleEngine()
            self.disorder_criteria = engine.get_disorder_criteria(disorder_code)
            
            if self.disorder_criteria:
                # Initialize responses
                for criterion in self.disorder_criteria.criteria:
                    self.criterion_responses[criterion.id] = False
                
                # Update form
                self.assessment_form["primary_diagnosis"] = disorder_code
                
                logger.info(f"Selected disorder: {disorder_code}")
            else:
                self.show_error("Disorder criteria not found")
                
        except Exception as e:
            logger.error(f"Failed to load disorder criteria: {e}")
            self.show_error("Failed to load disorder criteria")
    
    def update_criterion_response(self, criterion_id: str, value: bool):
        """Update DSM-5-TR criterion response and re-evaluate"""
        self.criterion_responses[criterion_id] = value
        
        # Auto-evaluate criteria
        if self.selected_disorder:
            self.evaluate_dsm5_criteria()
    
    def evaluate_dsm5_criteria(self):
        """Evaluate current DSM-5-TR criterion responses"""
        if not self.selected_disorder or not self.criterion_responses:
            return
        
        try:
            engine = DSM5RuleEngine()
            self.dsm5_evaluation = engine.evaluate_criteria(
                self.selected_disorder, 
                self.criterion_responses
            )
            
            logger.debug(f"DSM-5 evaluation: {self.dsm5_evaluation}")
            
        except Exception as e:
            logger.error(f"DSM-5 evaluation failed: {e}")
            self.show_error("Failed to evaluate criteria")
    
    def create_assessment(self):
        """Create new clinical assessment with DSM-5-TR results"""
        self.is_loading = True
        self.clear_messages()
        
        try:
            # Validate required fields
            if not self.assessment_form["patient_id"]:
                raise ValueError("Patient ID is required")
            if not self.assessment_form["chief_complaint"].strip():
                raise ValueError("Chief complaint is required")
            if not self.assessment_form["history_present_illness"].strip():
                raise ValueError("History of present illness is required")
            
            # Create assessment data
            assessment_data = PresentIllnessData(
                patient_id=self.assessment_form["patient_id"],
                assessment_date=date.fromisoformat(self.assessment_form["assessment_date"]),
                chief_complaint=self.assessment_form["chief_complaint"],
                history_present_illness=self.assessment_form["history_present_illness"],
                primary_diagnosis=self.assessment_form["primary_diagnosis"],
                secondary_diagnoses=self.assessment_form["secondary_diagnoses"],
                treatment_plan=self.assessment_form["treatment_plan"]
            )
            
            # Use clinical service with proper database session
            with get_db_session_with_user(self.current_user_id) as db:
                audit_service = AuditService(db, self.current_user_id)
                clinical_service = ClinicalService(db, audit_service, self.current_user_id)

                # Create assessment with DSM-5 responses
                new_assessment = clinical_service.create_assessment(
                    assessment_data,
                    self.criterion_responses if self.selected_disorder else None
                )

                if new_assessment:
                    self.current_assessment = new_assessment
                    self.show_success("Assessment created successfully")
                    logger.info(f"Assessment created successfully: ID {new_assessment.id}")

                    # Reset form after successful creation
                    self.reset_assessment_form()
                else:
                    self.show_error("Failed to create assessment")
                    logger.error("Assessment creation failed")
            
        except ValueError as e:
            self.show_error(f"Validation error: {e}")
        except Exception as e:
            logger.error(f"Failed to create assessment: {e}")
            self.show_error("Failed to create assessment")
        finally:
            self.is_loading = False
    
    def load_patient_assessments(self, patient_id: int):
        """Load all assessments for a patient"""
        try:
            # Use clinical service with proper database session
            with get_db_session_with_user(self.current_user_id) as db:
                audit_service = AuditService(db, self.current_user_id)
                clinical_service = ClinicalService(db, audit_service, self.current_user_id)

                # Load patient assessments
                assessments = clinical_service.get_patient_assessments(patient_id)
                self.patient_assessments = assessments

                logger.info(f"Loaded {len(assessments)} assessments for patient {patient_id}")
            
        except Exception as e:
            logger.error(f"Failed to load patient assessments: {e}")
            self.show_error("Failed to load patient assessments")

    def load_assessment(self, assessment_id: int):
        """Load existing assessment for editing"""
        try:
            with get_db_session_with_user(self.current_user_id) as db:
                audit_service = AuditService(db, self.current_user_id)
                clinical_service = ClinicalService(db, audit_service, self.current_user_id)

                # Load assessment
                assessment = clinical_service.get_assessment(assessment_id)
                if assessment:
                    self.current_assessment = assessment

                    # Populate form
                    self.assessment_form = {
                        "patient_id": assessment.patient_id,
                        "assessment_date": assessment.assessment_date.isoformat(),
                        "chief_complaint": assessment.chief_complaint,
                        "history_present_illness": assessment.history_present_illness,
                        "primary_diagnosis": assessment.primary_diagnosis or "",
                        "secondary_diagnoses": assessment.secondary_diagnoses or "",
                        "treatment_plan": assessment.treatment_plan or ""
                    }

                    # Load disorder criteria if primary diagnosis exists
                    if assessment.primary_diagnosis:
                        self.select_disorder(assessment.primary_diagnosis)

                    self.show_success("Assessment loaded successfully")
                    logger.info(f"Assessment {assessment_id} loaded successfully")
                else:
                    self.show_error("Assessment not found or access denied")
                    logger.warning(f"Assessment {assessment_id} not found or access denied")

        except Exception as e:
            logger.error(f"Failed to load assessment: {e}")
            self.show_error("Failed to load assessment")

    def update_assessment(self, assessment_id: int):
        """Update existing assessment"""
        self.is_loading = True
        self.clear_messages()

        try:
            # Validate required fields
            if not self.assessment_form["chief_complaint"].strip():
                raise ValueError("Chief complaint is required")
            if not self.assessment_form["history_present_illness"].strip():
                raise ValueError("History of present illness is required")

            # Create assessment data
            assessment_data = PresentIllnessData(
                patient_id=self.assessment_form["patient_id"],
                assessment_date=date.fromisoformat(self.assessment_form["assessment_date"]),
                chief_complaint=self.assessment_form["chief_complaint"],
                history_present_illness=self.assessment_form["history_present_illness"],
                primary_diagnosis=self.assessment_form["primary_diagnosis"],
                secondary_diagnoses=self.assessment_form["secondary_diagnoses"],
                treatment_plan=self.assessment_form["treatment_plan"]
            )

            # Use clinical service with proper database session
            with get_db_session_with_user(self.current_user_id) as db:
                audit_service = AuditService(db, self.current_user_id)
                clinical_service = ClinicalService(db, audit_service, self.current_user_id)

                # Update assessment with DSM-5 responses
                updated_assessment = clinical_service.update_assessment(
                    assessment_id,
                    assessment_data,
                    self.criterion_responses if self.selected_disorder else None
                )

                if updated_assessment:
                    self.current_assessment = updated_assessment
                    self.show_success("Assessment updated successfully")
                    logger.info(f"Assessment updated successfully: ID {assessment_id}")
                else:
                    self.show_error("Failed to update assessment")
                    logger.error("Assessment update failed")

        except ValueError as e:
            self.show_error(f"Validation error: {e}")
        except Exception as e:
            logger.error(f"Failed to update assessment: {e}")
            self.show_error("Failed to update assessment")
        finally:
            self.is_loading = False
    
    def update_assessment_form(self, field: str, value: Any):
        """Update assessment form field"""
        self.assessment_form[field] = value
        
        # If primary diagnosis changed, update disorder selection
        if field == "primary_diagnosis" and value != self.selected_disorder:
            self.select_disorder(value)
    
    def reset_assessment_form(self):
        """Reset assessment form to defaults"""
        self.assessment_form = {
            "patient_id": 0,
            "assessment_date": "",
            "chief_complaint": "",
            "history_present_illness": "",
            "primary_diagnosis": "",
            "secondary_diagnoses": "",
            "treatment_plan": ""
        }
        self.selected_disorder = ""
        self.disorder_criteria = None
        self.criterion_responses = {}
        self.dsm5_evaluation = None
        self.current_assessment = None
    
    def show_success(self, message: str):
        """Show success notification"""
        self.success_message = message
    
    def show_error(self, message: str):
        """Show error notification"""
        self.error_message = message
    
    def clear_messages(self):
        """Clear all notification messages"""
        self.error_message = ""
        self.success_message = ""
    
    @rx.var
    def criteria_met_percentage(self) -> float:
        """Calculate percentage of criteria met for progress display"""
        if not self.dsm5_evaluation:
            return 0.0
        
        total_criteria = len(self.criterion_responses)
        if total_criteria == 0:
            return 0.0
        
        met_count = self.dsm5_evaluation.get('met_criteria_count', 0)
        return (met_count / total_criteria) * 100
    
    @rx.var
    def diagnosis_confidence(self) -> str:
        """Get diagnosis confidence level as string"""
        if not self.dsm5_evaluation:
            return "Not evaluated"
        
        if self.dsm5_evaluation.get('criteria_met', False):
            confidence = self.dsm5_evaluation.get('confidence_level', 0)
            if confidence >= 0.8:
                return "High confidence"
            elif confidence >= 0.6:
                return "Moderate confidence"
            else:
                return "Low confidence"
        else:
            return "Criteria not met"
    
    @rx.var
    def required_criteria_status(self) -> str:
        """Get status of required criteria"""
        if not self.dsm5_evaluation:
            return ""
        
        met = self.dsm5_evaluation.get('met_criteria_count', 0)
        required = self.dsm5_evaluation.get('required_criteria', 0)
        
        return f"{met}/{required} criteria met"
