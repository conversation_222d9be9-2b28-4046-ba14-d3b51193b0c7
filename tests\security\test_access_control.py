# tests/security/test_access_control.py
import pytest
from models.patient import PatientData, UserPatientAccess
from services.patient_service import PatientService
from services.audit_service import AuditService
from services.clinical_service import ClinicalService
from models.clinical import PresentIllnessData
from datetime import date

def test_patient_access_control(test_db, test_crypto_service):
    """Test that users can only access patients they have permission for"""
    # Create patient with user 1
    service_user1 = PatientService(test_db, user_id=1, crypto_service=test_crypto_service)
    patient_data = PatientData(
        name="<PERSON>",
        dob="1990-01-01",
        phone="**********"
    )
    patient = service_user1.create_patient(patient_data)
    
    # User 1 should be able to access the patient
    retrieved = service_user1.get_patient(patient.id)
    assert retrieved is not None
    assert retrieved.name == "<PERSON> Doe"
    
    # User 2 should NOT be able to access the patient
    service_user2 = PatientService(test_db, user_id=2, crypto_service=test_crypto_service)
    retrieved_user2 = service_user2.get_patient(patient.id)
    assert retrieved_user2 is None

def test_patient_search_access_control(test_db, test_crypto_service):
    """Test that search only returns accessible patients"""
    # Create patients with different users
    service_user1 = PatientService(test_db, user_id=1, crypto_service=test_crypto_service)
    service_user2 = PatientService(test_db, user_id=2, crypto_service=test_crypto_service)
    
    # User 1 creates a patient
    patient1 = service_user1.create_patient(PatientData(name="John Doe", dob="1990-01-01"))
    
    # User 2 creates a patient
    patient2 = service_user2.create_patient(PatientData(name="Jane Smith", dob="1985-05-15"))
    
    # User 1 search should only return their patient
    results_user1 = service_user1.search_patients("John")
    assert len(results_user1["patients"]) == 1
    assert results_user1["patients"][0].id == patient1.id
    
    # User 2 search should only return their patient
    results_user2 = service_user2.search_patients("Jane")
    assert len(results_user2["patients"]) == 1
    assert results_user2["patients"][0].id == patient2.id
    
    # Cross-user searches should return empty
    results_cross1 = service_user1.search_patients("Jane")
    assert len(results_cross1["patients"]) == 0
    
    results_cross2 = service_user2.search_patients("John")
    assert len(results_cross2["patients"]) == 0

def test_audit_logging_access_denied(test_db, test_crypto_service):
    """Test that access denied events are properly audited"""
    # Create patient with user 1
    service_user1 = PatientService(test_db, user_id=1, crypto_service=test_crypto_service)
    patient = service_user1.create_patient(PatientData(name="Test Patient", dob="1990-01-01"))
    
    # Try to access with user 2 (should be denied and audited)
    service_user2 = PatientService(test_db, user_id=2, crypto_service=test_crypto_service)
    result = service_user2.get_patient(patient.id)
    assert result is None
    
    # Check audit log for access denied event
    from models.audit import AuditLog
    from sqlmodel import select
    
    audit_logs = test_db.exec(
        select(AuditLog).where(
            AuditLog.user_id == 2,
            AuditLog.action == "ACCESS_DENIED",
            AuditLog.table_name == "patient",
            AuditLog.record_id == patient.id
        )
    ).all()
    
    assert len(audit_logs) >= 1
    assert audit_logs[0].success == True  # Audit logging succeeded
    assert audit_logs[0].user_id == 2

def test_user_patient_access_model(test_db):
    """Test UserPatientAccess model functionality"""
    # Create access record
    access = UserPatientAccess(
        user_id=1,
        patient_id=1,
        granted_by=1,
        is_active=True
    )
    
    test_db.add(access)
    test_db.commit()
    test_db.refresh(access)
    
    assert access.id is not None
    assert access.user_id == 1
    assert access.patient_id == 1
    assert access.is_active == True
    assert access.granted_at is not None

def test_duplicate_detection_access_control(test_db, test_crypto_service):
    """Test that duplicate detection respects access control"""
    # User 1 creates a patient
    service_user1 = PatientService(test_db, user_id=1, crypto_service=test_crypto_service)
    patient1 = service_user1.create_patient(PatientData(name="John Doe", dob="1990-01-01"))
    
    # User 2 tries to check for duplicates - should not see user 1's patient
    service_user2 = PatientService(test_db, user_id=2, crypto_service=test_crypto_service)
    similar_data = PatientData(name="John Doe", dob="1990-01-01")
    duplicates = service_user2.find_potential_duplicates(similar_data)
    
    # Should not find any duplicates because user 2 can't access user 1's patients
    assert len(duplicates) == 0

def test_encryption_failure_access_control(test_db, test_crypto_service):
    """Test access control when encryption fails"""
    # This test simulates what happens when encryption keys are wrong
    from security.encryption import EncryptionService, generate_salt
    
    # Create patient with correct encryption
    service_correct = PatientService(test_db, user_id=1, crypto_service=test_crypto_service)
    patient = service_correct.create_patient(PatientData(name="Test Patient", dob="1990-01-01"))
    
    # Try to access with wrong encryption service
    wrong_salt = generate_salt()
    wrong_crypto = EncryptionService("wrong_password", wrong_salt)
    service_wrong = PatientService(test_db, user_id=1, crypto_service=wrong_crypto)
    
    # Should raise exception when trying to decrypt
    with pytest.raises(Exception):
        service_wrong.get_patient(patient.id)

@pytest.mark.security
class TestClinicalAccessControl:
    """Test clinical data access control"""

    def test_clinical_assessment_access_control(self, test_db, test_user, test_crypto_service):
        """Test clinical assessments follow patient access control"""
        # Create patient and assessment with user 1
        audit_service = AuditService(test_db, test_user.id)
        patient_service = PatientService(test_db, audit_service, test_user.id, test_crypto_service)
        clinical_service = ClinicalService(test_db, audit_service, test_user.id)

        patient_data = PatientData(
            first_name="John",
            last_name="Doe",
            date_of_birth=date(1990, 1, 1),
            phone_number="555-1234"
        )
        patient = patient_service.create_patient(patient_data)

        assessment_data = PresentIllnessData(
            patient_id=patient.id,
            assessment_date=date.today(),
            chief_complaint="Test complaint",
            history_present_illness="Test history",
            treatment_plan="Test plan"
        )
        assessment = clinical_service.create_assessment(assessment_data)

        # User 1 should be able to access the assessment
        retrieved = clinical_service.get_assessment(assessment.id)
        assert retrieved is not None
        assert retrieved.chief_complaint == "Test complaint"

        # User 2 should NOT be able to access the assessment
        audit_service_2 = AuditService(test_db, 999)  # Different user
        clinical_service_2 = ClinicalService(test_db, audit_service_2, 999)

        retrieved_user2 = clinical_service_2.get_assessment(assessment.id)
        assert retrieved_user2 is None

    def test_patient_assessments_access_control(self, test_db, test_user, test_crypto_service):
        """Test patient assessments list follows access control"""
        # Create patient and assessments with user 1
        audit_service = AuditService(test_db, test_user.id)
        patient_service = PatientService(test_db, audit_service, test_user.id, test_crypto_service)
        clinical_service = ClinicalService(test_db, audit_service, test_user.id)

        patient_data = PatientData(
            first_name="Jane",
            last_name="Smith",
            date_of_birth=date(1985, 5, 15),
            phone_number="555-5678"
        )
        patient = patient_service.create_patient(patient_data)

        # Create multiple assessments
        for i in range(3):
            assessment_data = PresentIllnessData(
                patient_id=patient.id,
                assessment_date=date.today(),
                chief_complaint=f"Complaint {i}",
                history_present_illness=f"History {i}",
                treatment_plan=f"Plan {i}"
            )
            clinical_service.create_assessment(assessment_data)

        # User 1 should see all assessments
        assessments = clinical_service.get_patient_assessments(patient.id)
        assert len(assessments) == 3

        # User 2 should see no assessments
        audit_service_2 = AuditService(test_db, 999)
        clinical_service_2 = ClinicalService(test_db, audit_service_2, 999)

        assessments_user2 = clinical_service_2.get_patient_assessments(patient.id)
        assert len(assessments_user2) == 0

@pytest.mark.security
class TestRoleBasedAccess:
    """Test role-based access control"""

    def test_admin_access_override(self, test_db, test_crypto_service):
        """Test admin users can access all patients (if implemented)"""
        # This test documents intended admin behavior
        # Implementation would depend on specific admin access requirements

        # Create patient with regular user
        audit_service = AuditService(test_db, 1)
        patient_service = PatientService(test_db, audit_service, 1, test_crypto_service)

        patient_data = PatientData(
            first_name="Admin",
            last_name="Test",
            date_of_birth=date(1980, 1, 1),
            phone_number="555-9999"
        )
        patient = patient_service.create_patient(patient_data)

        # Regular user 2 cannot access
        audit_service_2 = AuditService(test_db, 2)
        patient_service_2 = PatientService(test_db, audit_service_2, 2, test_crypto_service)

        retrieved = patient_service_2.get_patient(patient.id)
        assert retrieved is None

        # Note: Admin access would require additional implementation
        # This test documents the expected behavior

    def test_supervisor_access_permissions(self, test_db, test_crypto_service):
        """Test supervisor access permissions"""
        # This test documents intended supervisor behavior
        # Implementation would depend on specific supervisor access requirements

        # Create patient with clinician
        audit_service = AuditService(test_db, 1)
        patient_service = PatientService(test_db, audit_service, 1, test_crypto_service)

        patient_data = PatientData(
            first_name="Supervisor",
            last_name="Test",
            date_of_birth=date(1975, 6, 30),
            phone_number="555-7777"
        )
        patient = patient_service.create_patient(patient_data)

        # Note: Supervisor access would require additional implementation
        # This test documents the expected behavior
        assert patient.id is not None

@pytest.mark.security
class TestDataIsolation:
    """Test data isolation between users"""

    def test_user_data_isolation(self, test_db, test_crypto_service):
        """Test complete data isolation between users"""
        # Create patients with different users
        audit_service_1 = AuditService(test_db, 1)
        audit_service_2 = AuditService(test_db, 2)

        patient_service_1 = PatientService(test_db, audit_service_1, 1, test_crypto_service)
        patient_service_2 = PatientService(test_db, audit_service_2, 2, test_crypto_service)

        # User 1 creates patients
        for i in range(5):
            patient_data = PatientData(
                first_name=f"User1Patient{i}",
                last_name="Test",
                date_of_birth=date(1990, 1, i+1),
                phone_number=f"555-100{i}"
            )
            patient_service_1.create_patient(patient_data)

        # User 2 creates patients
        for i in range(3):
            patient_data = PatientData(
                first_name=f"User2Patient{i}",
                last_name="Test",
                date_of_birth=date(1985, 1, i+1),
                phone_number=f"555-200{i}"
            )
            patient_service_2.create_patient(patient_data)

        # User 1 should only see their patients
        results_1, count_1 = patient_service_1.search_patients("User1", limit=10, offset=0)
        assert count_1 == 5
        assert all("User1" in p.first_name for p in results_1)

        # User 2 should only see their patients
        results_2, count_2 = patient_service_2.search_patients("User2", limit=10, offset=0)
        assert count_2 == 3
        assert all("User2" in p.first_name for p in results_2)

        # Cross-user searches should return nothing
        cross_results_1, cross_count_1 = patient_service_1.search_patients("User2", limit=10, offset=0)
        assert cross_count_1 == 0

        cross_results_2, cross_count_2 = patient_service_2.search_patients("User1", limit=10, offset=0)
        assert cross_count_2 == 0
