#!/usr/bin/env python3
"""
Simple runner for the Psychiatry EMR application
Bypasses complex initialization for testing purposes
"""

import os
import sys
import reflex as rx
from pathlib import Path

# Set environment variables for testing
os.environ["MASTER_PASSWORD"] = "testpassword123"
os.environ["DATABASE_URL"] = "sqlite:///test_psychiatry_emr.db"
os.environ["DEBUG_MODE"] = "true"
os.environ["LOG_LEVEL"] = "DEBUG"
os.environ["ENCRYPTION_SALT"] = "a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456"
os.environ["SECRET_KEY"] = "test_secret_key_32_characters_long_for_development_only"
os.environ["SESSION_TIMEOUT_MINUTES"] = "60"
os.environ["MAX_LOGIN_ATTEMPTS"] = "10"
os.environ["LOCKOUT_DURATION_MINUTES"] = "5"

# Create a simple Reflex app for testing
app = rx.App()

@rx.page("/")
def index():
    return rx.container(
        rx.vstack(
            rx.heading("🏥 Psychiatry EMR - Test Mode", size="9"),
            rx.text("Application is running in test mode", size="5", color="green"),
            rx.text("✅ Server is accessible", size="4"),
            rx.text("✅ Basic routing works", size="4"),
            rx.text("✅ Ready for TestSprite testing", size="4"),
            rx.divider(),
            rx.link("Health Check", href="/health"),
            spacing="4",
            align="center",
            min_height="100vh",
            justify="center",
        ),
        max_width="800px",
        margin="0 auto",
        padding="2rem",
    )

@rx.page("/health")
def health():
    return rx.text("OK - Application is running")

if __name__ == "__main__":
    print("🚀 Starting Psychiatry EMR in test mode...")
    print("📊 Server will be available at http://localhost:3000")
    # Use reflex run command instead
    import subprocess
    subprocess.run([sys.executable, "-m", "reflex", "run", "--backend-port", "8000", "--frontend-port", "3000"])
