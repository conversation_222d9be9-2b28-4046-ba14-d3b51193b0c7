{"tech_stack": ["Python", "Reflex", "SQLModel", "PostgreSQL", "SQLite", "Pydantic", "FastAPI", "<PERSON><PERSON><PERSON>", "Alembic", "Cryptography", "PyYAML", "<PERSON><PERSON>", "Redis", "BCrypt"], "features": [{"name": "Authentication System", "description": "Secure user authentication with master password encryption and session management", "files": ["states/auth_state.py", "security/encryption.py", "main.py"], "api_doc": {"openapi": "3.0.0", "info": {"title": "Authentication API", "version": "1.0.0"}, "paths": {"/login": {"post": {"summary": "User login", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"username": {"type": "string"}, "password": {"type": "string"}}}}}}}}}}}, {"name": "Patient Management", "description": "Comprehensive patient record management with encryption and duplicate detection", "files": ["models/patient.py", "services/patient_service.py", "states/patient_state.py", "pages/patient_search.py", "components/patient_form.py"], "api_doc": {"openapi": "3.0.0", "info": {"title": "Patient Management API", "version": "1.0.0"}, "paths": {"/patients": {"get": {"summary": "Search patients", "parameters": [{"name": "search_term", "in": "query", "schema": {"type": "string"}}]}, "post": {"summary": "Create new patient", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PatientData"}}}}}}}, "components": {"schemas": {"PatientData": {"type": "object", "properties": {"name": {"type": "string"}, "dob": {"type": "string", "format": "date"}, "address": {"type": "string"}, "phone": {"type": "string"}, "email": {"type": "string"}, "education": {"type": "string"}, "occupation": {"type": "string"}, "living_situation": {"type": "string"}}}}}}}, {"name": "Clinical Assessment", "description": "DSM-5-TR integrated clinical assessments with intelligent forms", "files": ["models/clinical.py", "services/clinical_service.py", "services/dsm5_engine.py", "states/clinical_state.py", "pages/clinical_assessment.py", "components/dsm5_forms.py", "config/dsm5_criteria.yaml"], "api_doc": {"openapi": "3.0.0", "info": {"title": "Clinical Assessment API", "version": "1.0.0"}, "paths": {"/assessments": {"post": {"summary": "Create clinical assessment", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PresentIllnessData"}}}}}}, "/dsm5/disorders": {"get": {"summary": "Get available DSM-5 disorders"}}, "/dsm5/criteria/{disorder}": {"get": {"summary": "Get criteria for specific disorder"}}}, "components": {"schemas": {"PresentIllnessData": {"type": "object", "properties": {"patient_id": {"type": "integer"}, "assessment_date": {"type": "string", "format": "date"}, "chief_complaint": {"type": "string"}, "history_present_illness": {"type": "string"}, "primary_diagnosis": {"type": "string"}, "secondary_diagnoses": {"type": "string"}, "treatment_plan": {"type": "string"}}}}}}}, {"name": "Dashboard", "description": "Main dashboard with patient overview and quick access to features", "files": ["pages/dashboard.py"], "api_doc": {"openapi": "3.0.0", "info": {"title": "Dashboard API", "version": "1.0.0"}, "paths": {"/dashboard": {"get": {"summary": "Get dashboard data"}}}}}, {"name": "Audit System", "description": "Comprehensive audit logging for all user actions and data changes", "files": ["models/audit.py", "services/audit_service.py"], "api_doc": {"openapi": "3.0.0", "info": {"title": "Audit API", "version": "1.0.0"}, "paths": {"/audit/logs": {"get": {"summary": "Get audit logs"}}}}}, {"name": "Database Management", "description": "Database connection, migration, and performance optimization", "files": ["services/database.py", "alembic/env.py", "sql/performance_indexes.sql", "sql/setup_security.sql"], "api_doc": {"openapi": "3.0.0", "info": {"title": "Database API", "version": "1.0.0"}, "paths": {"/health": {"get": {"summary": "Database health check"}}}}}, {"name": "Security & Encryption", "description": "Multi-layer encryption, master password authentication, and security monitoring", "files": ["security/encryption.py", "monitoring/security_monitor.py"], "api_doc": {"openapi": "3.0.0", "info": {"title": "Security API", "version": "1.0.0"}, "paths": {"/security/status": {"get": {"summary": "Security status check"}}}}}]}