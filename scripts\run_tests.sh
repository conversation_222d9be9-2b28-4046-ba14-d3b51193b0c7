#!/bin/bash
# scripts/run_tests.sh - Comprehensive testing script

echo "🧪 Running comprehensive test suite..."

# Create reports directory
mkdir -p reports

# Security tests
echo "🔒 Running security tests..."
pytest tests/security/ -v --tb=short

# Integration tests
echo "🔗 Running integration tests..."
pytest tests/integration/ -v

# Performance benchmarks
echo "⚡ Running performance benchmarks..."
pytest tests/performance/ -v --benchmark-only --benchmark-json=reports/benchmark.json

# Generate coverage report
echo "📊 Generating coverage report..."
pytest --cov=. --cov-report=html:reports/coverage --cov-report=xml

# Generate HTML test report
echo "📊 Generating test report..."
pytest --html=reports/test_report.html --self-contained-html

echo "✅ All tests completed! Check reports/ directory for detailed results."
