#!/bin/bash
# Deployment Script for Psychiatry EMR
# Handles environment setup, building, and deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT="${ENVIRONMENT:-development}"
COMPOSE_FILE="docker-compose.yml"
ENV_FILE=".env"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose is not installed"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Setup environment
setup_environment() {
    log_info "Setting up environment: $ENVIRONMENT"
    
    case $ENVIRONMENT in
        "production")
            COMPOSE_FILE="docker-compose.prod.yml"
            ENV_FILE=".env.production"
            ;;
        "development")
            COMPOSE_FILE="docker-compose.yml"
            ENV_FILE=".env.development"
            ;;
        *)
            log_error "Unknown environment: $ENVIRONMENT"
            exit 1
            ;;
    esac
    
    # Check if environment file exists
    if [ ! -f "$ENV_FILE" ]; then
        if [ -f "${ENV_FILE}.template" ]; then
            log_warning "Environment file $ENV_FILE not found, copying from template"
            cp "${ENV_FILE}.template" "$ENV_FILE"
            log_warning "Please edit $ENV_FILE with your configuration before continuing"
            exit 1
        else
            log_error "Environment file $ENV_FILE not found and no template available"
            exit 1
        fi
    fi
    
    # Copy environment file to .env for Docker Compose
    cp "$ENV_FILE" ".env"
    
    log_success "Environment setup completed"
}

# Generate secure secrets
generate_secrets() {
    log_info "Checking for secure secrets..."
    
    # Check if we need to generate secrets
    if grep -q "your_.*_here" .env 2>/dev/null; then
        log_warning "Found placeholder values in .env file"
        
        if [ "$ENVIRONMENT" = "production" ]; then
            log_error "Production deployment requires secure secrets. Please update .env file."
            exit 1
        else
            log_info "Development environment - using development defaults"
        fi
    fi
    
    log_success "Secrets validation completed"
}

# Build images
build_images() {
    log_info "Building Docker images..."
    
    if [ -f "scripts/docker-build.sh" ]; then
        if [ "$ENVIRONMENT" = "production" ]; then
            bash scripts/docker-build.sh prod
        else
            bash scripts/docker-build.sh dev
        fi
    else
        # Fallback to direct docker build
        docker build -t psychiatry-emr:latest .
    fi
    
    log_success "Images built successfully"
}

# Start services
start_services() {
    log_info "Starting services with $COMPOSE_FILE..."
    
    # Use docker compose (new) or docker-compose (legacy)
    if docker compose version &> /dev/null; then
        DOCKER_COMPOSE="docker compose"
    else
        DOCKER_COMPOSE="docker-compose"
    fi
    
    # Stop any existing services
    $DOCKER_COMPOSE -f "$COMPOSE_FILE" down
    
    # Start services
    $DOCKER_COMPOSE -f "$COMPOSE_FILE" up -d
    
    log_success "Services started successfully"
}

# Health check
health_check() {
    log_info "Performing health check..."
    
    # Wait for services to start
    sleep 10
    
    # Check application health
    for i in {1..30}; do
        if curl -f http://localhost:3000/health &> /dev/null; then
            log_success "Application is healthy"
            return 0
        fi
        
        log_info "Waiting for application to start... ($i/30)"
        sleep 2
    done
    
    log_error "Health check failed - application not responding"
    return 1
}

# Show logs
show_logs() {
    log_info "Showing service logs..."
    
    if docker compose version &> /dev/null; then
        docker compose -f "$COMPOSE_FILE" logs -f
    else
        docker-compose -f "$COMPOSE_FILE" logs -f
    fi
}

# Stop services
stop_services() {
    log_info "Stopping services..."
    
    if docker compose version &> /dev/null; then
        docker compose -f "$COMPOSE_FILE" down
    else
        docker-compose -f "$COMPOSE_FILE" down
    fi
    
    log_success "Services stopped"
}

# Show status
show_status() {
    log_info "Service status:"
    
    if docker compose version &> /dev/null; then
        docker compose -f "$COMPOSE_FILE" ps
    else
        docker-compose -f "$COMPOSE_FILE" ps
    fi
}

# Show usage
usage() {
    echo "Usage: $0 [OPTIONS] COMMAND"
    echo ""
    echo "Commands:"
    echo "  start       Start the application"
    echo "  stop        Stop the application"
    echo "  restart     Restart the application"
    echo "  logs        Show application logs"
    echo "  status      Show service status"
    echo "  health      Check application health"
    echo ""
    echo "Options:"
    echo "  -e ENV      Set environment (development|production)"
    echo "  -h          Show this help"
    echo ""
    echo "Environment Variables:"
    echo "  ENVIRONMENT  Deployment environment (default: development)"
}

# Parse command line arguments
while getopts "e:h" opt; do
    case $opt in
        e)
            ENVIRONMENT="$OPTARG"
            ;;
        h)
            usage
            exit 0
            ;;
        \?)
            log_error "Invalid option: -$OPTARG"
            usage
            exit 1
            ;;
    esac
done

shift $((OPTIND-1))

# Main execution
main() {
    case "${1:-}" in
        "start")
            check_prerequisites
            setup_environment
            generate_secrets
            build_images
            start_services
            health_check
            log_success "Deployment completed successfully!"
            log_info "Application available at: http://localhost:3000"
            ;;
        "stop")
            setup_environment
            stop_services
            ;;
        "restart")
            setup_environment
            stop_services
            start_services
            health_check
            ;;
        "logs")
            setup_environment
            show_logs
            ;;
        "status")
            setup_environment
            show_status
            ;;
        "health")
            health_check
            ;;
        "")
            log_error "No command specified"
            usage
            exit 1
            ;;
        *)
            log_error "Unknown command: $1"
            usage
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
