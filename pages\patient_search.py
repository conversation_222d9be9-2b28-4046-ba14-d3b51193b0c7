"""
Psychiatry EMR - Patient Search Page
Advanced search, pagination, and patient selection interface.
"""

import reflex as rx
from datetime import date

from states.patient_state import PatientState
from states.auth_state import AuthState
from components.patient_form import patient_details_card

def patient_search_page() -> rx.Component:
    """Patient search page with advanced filtering"""
    return rx.vstack(
        # Page header
        page_header(),
        
        # Main content
        rx.container(
            rx.vstack(
                # Search and filters section
                search_filters_section(),
                
                # Results section
                search_results_section(),
                
                spacing="6",
                width="100%"
            ),
            max_width="1200px",
            p=6
        ),
        
        width="100%",
        min_height="100vh",
        bg="gray.50"
    )

def page_header() -> rx.Component:
    """Page header with navigation"""
    return rx.box(
        rx.container(
            rx.hstack(
                # Breadcrumb navigation
                rx.hstack(
                    rx.link("Dashboard", href="/dashboard", color="blue.600"),
                    rx.text("/", color="gray.400"),
                    rx.text("Patient Search", font_weight="bold"),
                    spacing="2"
                ),
                
                rx.spacer(),
                
                # New patient button
                rx.button(
                    rx.hstack(
                        rx.icon("user-plus", size=20),
                        rx.text("New Patient"),
                        spacing="2"
                    ),
                    color_scheme="blue",
                    size="3"
                ),
                
                width="100%",
                align="center"
            ),
            max_width="1200px"
        ),
        width="100%",
        bg="white",
        border_bottom="1px solid",
        border_color="gray.200",
        p=4
    )

def search_filters_section() -> rx.Component:
    """Advanced search and filtering section"""
    return rx.card(
        rx.vstack(
            rx.heading("Patient Search", size="6", mb=4),
            
            # Basic search
            rx.hstack(
                rx.input(
                    placeholder="Search by name, phone, email, or patient ID...",
                    value=PatientState.search_term,
                    on_change=PatientState.set_search_term,
                    size="3",
                    width="100%"
                ),
                rx.button(
                    "Search",
                    on_click=PatientState.search_patients,
                    loading=PatientState.is_loading,
                    color_scheme="blue",
                    size="3"
                ),
                width="100%",
                spacing="3"
            ),

            # Advanced filters (expandable)
            rx.vstack(
                rx.button(
                    rx.hstack(
                        rx.text("Advanced Filters"),
                        rx.icon("chevron-down", size=16),
                        spacing="2"
                    ),
                    variant="ghost",
                    size="2",
                    on_click=PatientState.toggle_advanced_filters,
                    width="100%"
                ),
                rx.cond(
                    PatientState.show_advanced_filters,
                    advanced_filters_component()
                ),
                width="100%"
            ),

            spacing="4",
            width="100%"
        ),
        p=6,
        width="100%"
    )

def advanced_filters_component() -> rx.Component:
    """Advanced filtering options"""
    return rx.vstack(
        rx.grid(
            # Age range filter
            rx.vstack(
                rx.text("Age Range", font_weight="bold"),
                rx.hstack(
                    rx.input(
                        placeholder="Min age",
                        type="number",
                        width="100%"
                    ),
                    rx.text("to"),
                    rx.input(
                        placeholder="Max age",
                        type="number",
                        width="100%"
                    ),
                    spacing="2",
                    width="100%"
                ),
                spacing="2",
                align="start"
            ),

            # Gender filter
            rx.vstack(
                rx.text("Gender", font_weight="bold"),
                rx.select(
                    ["Any", "Male", "Female", "Non-binary", "Other"],
                    placeholder="Select gender",
                    width="100%"
                ),
                spacing="2",
                align="start"
            ),

            # Date range filter
            rx.vstack(
                rx.text("Registration Date", font_weight="bold"),
                rx.hstack(
                    rx.input(
                        type="date",
                        width="100%"
                    ),
                    rx.text("to"),
                    rx.input(
                        type="date",
                        width="100%"
                    ),
                    spacing="2",
                    width="100%"
                ),
                spacing="2",
                align="start"
            ),

            # Status filter
            rx.vstack(
                rx.text("Status", font_weight="bold"),
                rx.select(
                    ["All", "Active", "Inactive"],
                    placeholder="Select status",
                    width="100%"
                ),
                spacing="2",
                align="start"
            ),
            
            columns="4",
            spacing="4",
            width="100%"
        ),
        
        # Filter actions
        rx.hstack(
            rx.button(
                "Apply Filters",
                color_scheme="blue",
                size="2"
            ),
            rx.button(
                "Clear Filters",
                variant="outline",
                size="2"
            ),
            spacing="2"
        ),

        spacing="4",
        width="100%",
        p=4,
        border="1px solid",
        border_color="gray.200",
        border_radius="md",
        bg="gray.50"
    )

def search_results_section() -> rx.Component:
    """Search results with pagination"""
    return rx.vstack(
        # Results header
        rx.cond(
            PatientState.search_results,
            rx.card(
                rx.hstack(
                    rx.vstack(
                        rx.heading(f"Search Results ({PatientState.total_results})", size="5"),
                        rx.text(f"Showing page {PatientState.current_page} of {PatientState.total_pages}", color="gray.600"),
                        spacing="1",
                        align="start"
                    ),
                    
                    rx.spacer(),
                    
                    # View options
                    rx.hstack(
                        rx.text("View:", font_size="sm"),
                        rx.hstack(
                            rx.button("List", size="2", variant="outline"),
                            rx.button("Cards", size="2", variant="solid"),
                            spacing="1"
                        ),
                        spacing="2",
                        align="center"
                    ),
                    
                    width="100%",
                    align="center"
                ),
                p=4,
                width="100%"
            )
        ),
        
        # Results content
        rx.cond(
            PatientState.search_results,
            rx.vstack(
                # Results grid/list
                results_display_component(),
                
                # Pagination
                pagination_component(),
                
                spacing="4",
                width="100%"
            ),
            # No results state
            no_results_component()
        ),
        
        spacing="4",
        width="100%"
    )

def results_display_component() -> rx.Component:
    """Display search results in card format"""
    return rx.grid(
        rx.foreach(
            PatientState.search_results,
            lambda patient: patient_result_card(patient)
        ),
        columns="1",
        spacing="4",
        width="100%"
    )

def patient_result_card(patient) -> rx.Component:
    """Individual patient result card"""
    return rx.card(
        rx.hstack(
            # Patient info
            rx.vstack(
                rx.hstack(
                    rx.heading(patient.name, size="5"),
                    rx.badge(f"ID: {patient.id}", color_scheme="blue", size="1"),
                    spacing="3"
                ),

                rx.grid(
                    rx.cond(
                        patient.dob,
                        rx.text(f"DOB: {patient.dob}", color="gray.600"),
                        rx.text("DOB: Not provided", color="gray.600")
                    ),
                    rx.cond(
                        patient.phone,
                        rx.text(f"Phone: {patient.phone}", color="gray.600"),
                        rx.text("Phone: Not provided", color="gray.600")
                    ),
                    rx.cond(
                        patient.email,
                        rx.text(f"Email: {patient.email}", color="gray.600"),
                        rx.text("Email: Not provided", color="gray.600")
                    ),
                    rx.cond(
                        patient.created_at,
                        rx.text(f"Created: {patient.created_at}", color="gray.600"),
                        rx.text("Created: Not provided", color="gray.600")
                    ),

                    columns="2",
                    spacing="2",
                    width="100%"
                ),

                spacing="2",
                align="start",
                flex="1"
            ),
            
            # Actions
            rx.vstack(
                rx.button(
                    "View Details",
                    on_click=lambda: PatientState.load_patient(patient.id),
                    color_scheme="blue",
                    size="2",
                    width="100%"
                ),
                rx.button(
                    "New Assessment",
                    color_scheme="green",
                    size="2",
                    width="100%"
                ),
                rx.button(
                    "Edit Patient",
                    color_scheme="gray",
                    size="2",
                    width="100%"
                ),
                spacing="2",
                min_width="150px"
            ),

            width="100%",
            align="start",
            spacing="4"
        ),
        p=4,
        width="100%",
        _hover={"shadow": "md"}
    )

def pagination_component() -> rx.Component:
    """Pagination controls"""
    return rx.card(
        rx.hstack(
            # Page info
            rx.text(f"Page {PatientState.current_page} of {PatientState.total_pages}", color="gray.600"),
            
            rx.spacer(),
            
            # Pagination buttons
            rx.hstack(
                rx.button(
                    rx.icon("chevron-left", size=16),
                    on_click=PatientState.prev_page,
                    disabled=PatientState.current_page == 1,
                    size="2",
                    variant="outline"
                ),

                # Page numbers (simplified - show current and nearby pages)
                rx.hstack(
                    rx.cond(
                        PatientState.current_page > 1,
                        rx.button(
                            "1",
                            on_click=lambda: PatientState.go_to_page(1),
                            size="2",
                            variant="outline"
                        )
                    ),

                    rx.cond(
                        PatientState.current_page > 3,
                        rx.text("...", color="gray.400")
                    ),

                    rx.button(
                        PatientState.current_page,
                        size="2",
                        color_scheme="blue"
                    ),

                    rx.cond(
                        PatientState.current_page < PatientState.total_pages - 2,
                        rx.text("...", color="gray.400")
                    ),

                    rx.cond(
                        PatientState.current_page < PatientState.total_pages,
                        rx.button(
                            PatientState.total_pages,
                            on_click=lambda: PatientState.go_to_page(PatientState.total_pages),
                            size="2",
                            variant="outline"
                        )
                    ),

                    spacing="1"
                ),

                rx.button(
                    rx.icon("chevron-right", size=16),
                    on_click=PatientState.next_page,
                    disabled=PatientState.current_page == PatientState.total_pages,
                    size="2",
                    variant="outline"
                ),

                spacing="2"
            ),
            
            width="100%",
            align="center"
        ),
        p=3,
        width="100%"
    )

def no_results_component() -> rx.Component:
    """No results found state"""
    return rx.card(
        rx.vstack(
            rx.icon("search-x", size=48, color="gray.400"),
            rx.heading("No patients found", size="5", color="gray.600"),
            rx.text("Try adjusting your search terms or filters", color="gray.500"),
            rx.button(
                "Create New Patient",
                color_scheme="blue",
                size="3",
                mt=4
            ),
            spacing="3",
            align="center"
        ),
        p=8,
        width="100%",
        text_align="center"
    )
