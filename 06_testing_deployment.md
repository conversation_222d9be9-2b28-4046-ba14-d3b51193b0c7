# Testing Strategy & Production Deployment

## Enhanced Testing Strategy

### Test Configuration and Fixtures

```python
# tests/conftest.py - Test configuration and fixtures
import pytest
import tempfile
import os
from pathlib import Path
from sqlmodel import Session, create_engine, SQLModel
from security.encryption import EncryptionService, generate_salt
from config.settings import Settings

@pytest.fixture(scope="session")
def test_settings():
    """Test settings with secure defaults"""
    return Settings(
        database_url="sqlite:///test.db",
        encryption_salt=generate_salt().hex(),
        secret_key="test_secret_key_32_characters_long",
        debug_mode=True,
        log_level="DEBUG"
    )

@pytest.fixture(scope="session")
def test_crypto_service():
    """Test encryption service"""
    salt = generate_salt()
    return EncryptionService("test_password", salt)

@pytest.fixture
def test_db(test_settings):
    """Test database session"""
    engine = create_engine(test_settings.database_url, echo=False)
    SQLModel.metadata.create_all(engine)
    
    with Session(engine) as session:
        yield session
    
    # Cleanup
    if test_settings.database_url.startswith("sqlite"):
        db_path = test_settings.database_url.replace("sqlite:///", "")
        if os.path.exists(db_path):
            os.remove(db_path)

@pytest.fixture
def sample_patient_data():
    """Sample patient data for testing"""
    return {
        "name": "John Doe",
        "dob": "1990-01-01",
        "phone": "**********",
        "email": "<EMAIL>",
        "address": "123 Main St, Anytown, USA"
    }
```

### Security Integration Tests

```python
# tests/security/test_encryption.py
import pytest
from security.encryption import EncryptionService, generate_salt

def test_encryption_roundtrip():
    """Test encryption and decryption work correctly"""
    salt = generate_salt()
    service = EncryptionService("test_password", salt)
    original = "Test Patient Name"
    
    encrypted = service.encrypt(original)
    assert encrypted != original
    assert len(encrypted) > 0
    
    decrypted = service.decrypt(encrypted)
    assert decrypted == original

def test_encryption_fails_gracefully():
    """Test encryption handles errors appropriately"""
    salt = generate_salt()
    service = EncryptionService("test_password", salt)
    
    # Test with invalid encrypted data
    with pytest.raises(Exception):
        service.decrypt("invalid_encrypted_data")

def test_different_passwords_different_encryption():
    """Test that different passwords produce different encrypted results"""
    salt = generate_salt()
    service1 = EncryptionService("password1", salt)
    service2 = EncryptionService("password2", salt)
    
    original = "Test Data"
    encrypted1 = service1.encrypt(original)
    encrypted2 = service2.encrypt(original)
    
    assert encrypted1 != encrypted2

# tests/security/test_access_control.py
import pytest
from sqlmodel import Session
from services.patient_service import PatientService
from models.patient import Patient, PatientData, UserPatientAccess

def test_user_cannot_access_unauthorized_patient(test_db: Session, test_crypto_service):
    """Test that users cannot access patients they're not authorized for"""
    # Create patient accessible by user 1
    service1 = PatientService(test_db, user_id=1, crypto_service=test_crypto_service)
    patient_data = PatientData(name="Test Patient", dob="1990-01-01")
    created_patient = service1.create_patient(patient_data)
    
    # Try to access with different user
    service2 = PatientService(test_db, user_id=2, crypto_service=test_crypto_service)
    result = service2.get_patient(created_patient.id)
    
    assert result is None  # Should not have access

def test_audit_logs_access_attempts(test_db: Session, test_crypto_service):
    """Test that all access attempts are logged"""
    service = PatientService(test_db, user_id=1, crypto_service=test_crypto_service)
    
    # This should generate audit logs
    service.search_patients("test")
    
    # Verify audit logs were created
    from models.audit import AuditLog
    from sqlmodel import select
    
    audit_logs = test_db.exec(select(AuditLog)).all()
    assert len(audit_logs) > 0

def test_patient_merge_security(test_db: Session, test_crypto_service):
    """Test that patient merging respects access control"""
    service1 = PatientService(test_db, user_id=1, crypto_service=test_crypto_service)
    service2 = PatientService(test_db, user_id=2, crypto_service=test_crypto_service)
    
    # User 1 creates two patients
    patient1 = service1.create_patient(PatientData(name="John Smith", dob="1990-01-01"))
    patient2 = service1.create_patient(PatientData(name="John Smith", dob="1990-01-01"))
    
    # User 2 cannot merge user 1's patients
    result = service2.merge_patients(patient1.id, patient2.id)
    assert result is False
```

### Integration Tests

```python
# tests/integration/test_patient_workflow.py
import pytest
from models.patient import PatientData
from services.patient_service import PatientService

def test_complete_patient_workflow(test_db, test_crypto_service):
    """Test complete patient creation and retrieval workflow"""
    service = PatientService(test_db, user_id=1, crypto_service=test_crypto_service)
    
    # Create patient
    patient_data = PatientData(
        name="John Doe",
        dob="1990-01-01",
        phone="**********",
        email="<EMAIL>"
    )
    
    created = service.create_patient(patient_data)
    assert created.id is not None
    assert created.name == "John Doe"
    assert created.phone == "(*************"  # Should be formatted
    
    # Retrieve patient
    retrieved = service.get_patient(created.id)
    assert retrieved is not None
    assert retrieved.name == created.name
    
    # Search for patient
    search_results = service.search_patients("John")
    assert len(search_results["patients"]) >= 1
    assert any(p.id == created.id for p in search_results["patients"])

def test_patient_merge_workflow(test_db, test_crypto_service):
    """Test patient merging functionality"""
    service = PatientService(test_db, user_id=1, crypto_service=test_crypto_service)
    
    # Create two similar patients
    patient1 = service.create_patient(PatientData(name="John Smith", dob="1990-01-01"))
    patient2 = service.create_patient(PatientData(name="John Smith", dob="1990-01-01"))
    
    # Merge patients
    success = service.merge_patients(patient1.id, patient2.id)
    assert success
    
    # Verify duplicate is marked inactive
    duplicate = service.get_patient(patient2.id)
    assert duplicate is None  # Should not be accessible (marked inactive)

def test_duplicate_detection(test_db, test_crypto_service):
    """Test duplicate patient detection"""
    service = PatientService(test_db, user_id=1, crypto_service=test_crypto_service)
    
    # Create original patient
    original = service.create_patient(PatientData(name="Jane Smith", dob="1985-05-15"))
    
    # Check for duplicates with similar data
    similar_data = PatientData(name="Jane Smith", dob="1985-05-15")
    duplicates = service.find_potential_duplicates(similar_data)
    
    assert len(duplicates) >= 1
    assert duplicates[0]["id"] == original.id
    assert duplicates[0]["similarity_score"] >= 85

# tests/services/test_dsm5_engine.py
import pytest
from services.dsm5_engine import DSM5RuleEngine, CriterionRule, DisorderCriteria

def test_dsm5_criteria_validation():
    """Test DSM-5-TR criteria validation"""
    # Test valid criterion
    criterion = CriterionRule(id="test_1", description="Test criterion", required=True)
    assert criterion.id == "test_1"
    
    # Test invalid criterion ID
    with pytest.raises(ValueError):
        CriterionRule(id="", description="Test", required=True)

def test_dsm5_evaluation():
    """Test diagnostic criteria evaluation"""
    engine = DSM5RuleEngine()
    
    # Test MDD evaluation
    responses = {
        'mdd_1': True,  # Depressed mood
        'mdd_2': True,  # Anhedonia
        'mdd_3': True,  # Weight changes
        'mdd_4': True,  # Sleep changes
        'mdd_5': True,  # Psychomotor changes
        'mdd_6': False, # Fatigue
        'mdd_7': False, # Guilt
        'mdd_8': False, # Concentration
        'mdd_9': False  # Suicidal ideation
    }
    
    result = engine.evaluate_criteria('296.22', responses)
    assert result['criteria_met'] == True  # 5 out of 9 criteria met
    assert result['met_criteria_count'] == 5
    assert result['disorder_code'] == '296.22'

def test_dsm5_insufficient_criteria():
    """Test DSM-5-TR evaluation with insufficient criteria"""
    engine = DSM5RuleEngine()
    
    # Only 3 criteria met (need 5 for MDD)
    responses = {
        'mdd_1': True,
        'mdd_2': True,
        'mdd_3': True,
        'mdd_4': False,
        'mdd_5': False,
        'mdd_6': False,
        'mdd_7': False,
        'mdd_8': False,
        'mdd_9': False
    }
    
    result = engine.evaluate_criteria('296.22', responses)
    assert result['criteria_met'] == False
    assert result['met_criteria_count'] == 3

# tests/integration/test_clinical_workflow.py
import pytest
from models.clinical import PresentIllnessData
from services.clinical_service import ClinicalService
from datetime import date

def test_clinical_assessment_creation(test_db, test_crypto_service):
    """Test clinical assessment creation with DSM-5-TR evaluation"""
    # First create a patient
    from services.patient_service import PatientService
    from models.patient import PatientData
    
    patient_service = PatientService(test_db, user_id=1, crypto_service=test_crypto_service)
    patient = patient_service.create_patient(PatientData(name="Test Patient", dob="1990-01-01"))
    
    # Create clinical assessment
    clinical_service = ClinicalService(test_db, user_id=1, crypto_service=test_crypto_service)
    
    assessment_data = PresentIllnessData(
        patient_id=patient.id,
        assessment_date=date.today(),
        chief_complaint="Feeling depressed",
        history_present_illness="Patient reports 2 weeks of depressed mood...",
        primary_diagnosis="296.22",
        treatment_plan="Start SSRI, therapy referral"
    )
    
    # DSM-5 responses
    dsm5_responses = {
        'mdd_1': True,
        'mdd_2': True,
        'mdd_3': True,
        'mdd_4': True,
        'mdd_5': True,
        'mdd_6': False,
        'mdd_7': False,
        'mdd_8': False,
        'mdd_9': False
    }
    
    created_assessment = clinical_service.create_assessment(assessment_data, dsm5_responses)
    
    assert created_assessment.id is not None
    assert created_assessment.patient_id == patient.id
    assert created_assessment.dsm5_criteria_met is not None
    assert created_assessment.dsm5_criteria_met['criteria_met'] == True
```

### Performance Tests

```python
# tests/performance/test_encryption_performance.py
import pytest
import time
from security.encryption import EncryptionService, generate_salt

def test_encryption_performance(benchmark):
    """Benchmark encryption performance"""
    salt = generate_salt()
    service = EncryptionService("test_password", salt)
    test_data = "John Doe - 123 Main St, Anytown, USA - <EMAIL>"
    
    result = benchmark(service.encrypt, test_data)
    assert len(result) > 0

def test_decryption_performance(benchmark):
    """Benchmark decryption performance"""
    salt = generate_salt()
    service = EncryptionService("test_password", salt)
    test_data = "John Doe - 123 Main St, Anytown, USA - <EMAIL>"
    encrypted = service.encrypt(test_data)
    
    result = benchmark(service.decrypt, encrypted)
    assert result == test_data

# tests/performance/test_database_performance.py
import pytest
from models.patient import PatientData
from services.patient_service import PatientService

def test_patient_search_performance(test_db, test_crypto_service, benchmark):
    """Benchmark patient search performance"""
    service = PatientService(test_db, user_id=1, crypto_service=test_crypto_service)
    
    # Create test patients
    for i in range(100):
        patient_data = PatientData(name=f"Test Patient {i}", dob="1990-01-01")
        service.create_patient(patient_data)
    
    # Benchmark search
    result = benchmark(service.search_patients, "Test")
    assert len(result["patients"]) > 0

def test_bulk_patient_creation(test_db, test_crypto_service, benchmark):
    """Benchmark bulk patient creation"""
    service = PatientService(test_db, user_id=1, crypto_service=test_crypto_service)
    
    def create_patients():
        patients = []
        for i in range(10):
            patient_data = PatientData(name=f"Bulk Patient {i}", dob="1990-01-01")
            patients.append(service.create_patient(patient_data))
        return patients
    
    result = benchmark(create_patients)
    assert len(result) == 10
```

## Production Deployment

### Docker Configuration

```dockerfile
# Dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create non-root user
RUN useradd -m -u 1000 emruser && chown -R emruser:emruser /app
USER emruser

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# Start application
CMD ["reflex", "run", "--host", "0.0.0.0"]
```

### Docker Compose for Development

```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=**************************************/psychiatry_emr
      - DEBUG_MODE=true
    depends_on:
      - db
    volumes:
      - ./logs:/app/logs
    networks:
      - emr_network

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=psychiatry_emr
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./sql:/docker-entrypoint-initdb.d
    networks:
      - emr_network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - emr_network

volumes:
  postgres_data:
  redis_data:

networks:
  emr_network:
    driver: bridge
```

### Production Security Checklist

#### Pre-Deployment Security Audit
- [ ] **Encryption Keys**: Master password is complex and stored securely
- [ ] **Environment Variables**: All secrets properly configured and not in code
- [ ] **Database**: PostgreSQL configured with SSL/TLS
- [ ] **Authentication**: Strong password policies enforced
- [ ] **Authorization**: RLS policies tested and verified
- [ ] **Audit Trail**: All sensitive operations logged
- [ ] **Data Backup**: Encrypted backup strategy implemented
- [ ] **Network Security**: Firewall rules configured
- [ ] **Application Security**: No debug mode in production
- [ ] **Dependencies**: All packages updated to latest secure versions
- [ ] **Error Handling**: No sensitive data exposed in error messages
- [ ] **HTTPS**: SSL/TLS certificates properly configured
- [ ] **Session Security**: Secure session configuration

#### Infrastructure Security
```bash
# Production deployment script
#!/bin/bash
set -e

echo "🚀 Deploying Psychiatry EMR to Production"

# Verify environment
if [ -z "$ENCRYPTION_SALT" ]; then
    echo "❌ ENCRYPTION_SALT not set"
    exit 1
fi

if [ -z "$SECRET_KEY" ]; then
    echo "❌ SECRET_KEY not set"
    exit 1
fi

# Database backup before deployment
echo "📁 Creating database backup..."
pg_dump $DATABASE_URL > backup_$(date +%Y%m%d_%H%M%S).sql

# Run security tests
echo "🔒 Running security tests..."
pytest tests/security/ -v

# Database migrations
echo "🗄️ Running database migrations..."
alembic upgrade head

# Deploy application
echo "📦 Deploying application..."
docker-compose -f docker-compose.prod.yml up -d

# Health check
echo "🏥 Performing health check..."
sleep 10
curl -f http://localhost:3000/health || exit 1

echo "✅ Deployment complete!"
```

### Ongoing Security Maintenance

#### Monitoring & Alerting
```python
# monitoring/security_monitor.py
import logging
from datetime import datetime, timedelta
from sqlmodel import Session, select
from models.audit import AuditLog

def check_failed_access_attempts():
    """Monitor for suspicious access patterns"""
    with get_db_session() as db:
        # Check for repeated failed access attempts
        recent_failures = db.exec(
            select(AuditLog).where(
                AuditLog.action == "ACCESS_DENIED",
                AuditLog.timestamp > datetime.now() - timedelta(minutes=15)
            )
        ).all()
        
        if len(recent_failures) > 10:
            logging.warning(f"High number of access denials: {len(recent_failures)}")
            # Send alert to security team

def check_bulk_data_access():
    """Monitor for unusual bulk data access"""
    with get_db_session() as db:
        # Check for users accessing many patients quickly
        bulk_access = db.exec(
            select(AuditLog).where(
                AuditLog.action == "READ",
                AuditLog.table_name == "patient",
                AuditLog.timestamp > datetime.now() - timedelta(minutes=5)
            )
        ).all()
        
        # Group by user and check volume
        user_access = {}
        for access in bulk_access:
            user_id = access.user_id
            user_access[user_id] = user_access.get(user_id, 0) + 1
        
        for user_id, count in user_access.items():
            if count > 20:  # More than 20 patient accesses in 5 minutes
                logging.warning(f"Bulk patient access by user {user_id}: {count} patients")
```

#### Backup & Recovery
```bash
#!/bin/bash
# backup_script.sh - Automated backup with encryption

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/secure/backups"
DATABASE_NAME="psychiatry_emr"

# Create encrypted database backup
echo "Creating encrypted database backup..."
pg_dump $DATABASE_NAME | \
gpg --symmetric --cipher-algo AES256 --compress-algo 1 \
--output "$BACKUP_DIR/db_backup_$DATE.sql.gpg"

# Backup application logs
tar -czf "$BACKUP_DIR/logs_backup_$DATE.tar.gz" /app/logs/

# Clean old backups (keep 30 days)
find $BACKUP_DIR -name "*.gpg" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete

echo "Backup completed: $DATE"
```

### Testing Commands & CI/CD

```bash
# Testing commands for CI/CD pipeline

# Install dependencies
pip install -r requirements.txt
pip install -r requirements-dev.txt

# Code quality checks
black --check .
ruff check .
mypy .

# Security tests
pytest tests/security/ -v --tb=short

# Integration tests
pytest tests/integration/ -v

# Performance benchmarks
pytest tests/performance/ -v --benchmark-only

# Generate coverage report
pytest --cov=. --cov-report=html --cov-report=xml

# Security vulnerability scan
safety check
bandit -r . -f json -o security_report.json

# Generate test report
pytest --html=reports/test_report.html --self-contained-html
```

This comprehensive testing and deployment strategy ensures the psychiatry EMR system maintains high security standards while providing reliable performance in production environments.