-- Psychiatry EMR - Database Security Setup
-- Enhanced security setup for PostgreSQL

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS pgcrypto;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS pg_trgm;  -- For text search performance

-- Create audit trigger function
CREATE OR REPLACE FUNCTION create_audit_trail()
RETURNS TRIGGER AS $audit$
BEGIN
    -- Log all data changes
    IF TG_OP = 'DELETE' THEN
        INSERT INTO audit_log (
            user_id, table_name, record_id, action, 
            old_values, timestamp
        ) VALUES (
            COALESCE(current_setting('app.current_user', true)::int, 0),
            TG_TABLE_NAME,
            OLD.id,
            TG_OP,
            row_to_json(OLD),
            NOW()
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO audit_log (
            user_id, table_name, record_id, action,
            old_values, new_values, timestamp
        ) VALUES (
            COALESCE(current_setting('app.current_user', true)::int, 0),
            TG_TABLE_NAME,
            NEW.id,
            TG_OP,
            row_to_json(OLD),
            row_to_json(NEW),
            NOW()
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO audit_log (
            user_id, table_name, record_id, action,
            new_values, timestamp
        ) VALUES (
            COALESCE(current_setting('app.current_user', true)::int, 0),
            TG_TABLE_NAME,
            NEW.id,
            TG_OP,
            row_to_json(NEW),
            NOW()
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$audit$ LANGUAGE plpgsql;

-- Apply audit triggers to sensitive tables
DO $$
BEGIN
    -- Only create triggers if tables exist
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'patient') THEN
        DROP TRIGGER IF EXISTS patient_audit_trigger ON patient;
        CREATE TRIGGER patient_audit_trigger
            AFTER INSERT OR UPDATE OR DELETE ON patient
            FOR EACH ROW EXECUTE FUNCTION create_audit_trail();
    END IF;

    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'user_patient_access') THEN
        DROP TRIGGER IF EXISTS user_patient_access_audit_trigger ON user_patient_access;
        CREATE TRIGGER user_patient_access_audit_trigger
            AFTER INSERT OR UPDATE OR DELETE ON user_patient_access
            FOR EACH ROW EXECUTE FUNCTION create_audit_trail();
    END IF;

    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'present_illness') THEN
        DROP TRIGGER IF EXISTS present_illness_audit_trigger ON present_illness;
        CREATE TRIGGER present_illness_audit_trigger
            AFTER INSERT OR UPDATE OR DELETE ON present_illness
            FOR EACH ROW EXECUTE FUNCTION create_audit_trail();
    END IF;
END $$;

-- Row Level Security policies
DO $$
BEGIN
    -- Enable RLS on tables if they exist
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'patient') THEN
        ALTER TABLE patient ENABLE ROW LEVEL SECURITY;

        -- Drop existing policy if it exists
        DROP POLICY IF EXISTS patient_access_policy ON patient;

        -- Create patient access policy
        CREATE POLICY patient_access_policy ON patient
            FOR ALL TO public
            USING (
                EXISTS (
                    SELECT 1 FROM user_patient_access upa
                    WHERE upa.user_id = COALESCE(current_setting('app.current_user', true)::int, 0)
                    AND upa.patient_id = patient.id
                    AND upa.is_active = true
                )
            );
    END IF;

    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'user_patient_access') THEN
        ALTER TABLE user_patient_access ENABLE ROW LEVEL SECURITY;

        -- Drop existing policy if it exists
        DROP POLICY IF EXISTS user_access_policy ON user_patient_access;

        -- Create user access policy
        CREATE POLICY user_access_policy ON user_patient_access
            FOR ALL TO public
            USING (user_id = COALESCE(current_setting('app.current_user', true)::int, 0));
    END IF;

    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'present_illness') THEN
        ALTER TABLE present_illness ENABLE ROW LEVEL SECURITY;

        -- Drop existing policy if it exists
        DROP POLICY IF EXISTS clinical_access_policy ON present_illness;

        -- Create clinical access policy
        CREATE POLICY clinical_access_policy ON present_illness
            FOR ALL TO public
            USING (
                EXISTS (
                    SELECT 1 FROM user_patient_access upa
                    WHERE upa.user_id = COALESCE(current_setting('app.current_user', true)::int, 0)
                    AND upa.patient_id = present_illness.patient_id
                    AND upa.is_active = true
                )
            );
    END IF;
END $$;

-- Create application role
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_roles WHERE rolname = 'application_role') THEN
        CREATE ROLE application_role;
    END IF;
END $$;

GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO application_role;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO application_role;

-- Create function to set current user for RLS
CREATE OR REPLACE FUNCTION set_current_user(user_id integer)
RETURNS void AS $$
BEGIN
    PERFORM set_config('app.current_user', user_id::text, false);
END;
$$ LANGUAGE plpgsql;

-- Create function to clear current user
CREATE OR REPLACE FUNCTION clear_current_user()
RETURNS void AS $$
BEGIN
    PERFORM set_config('app.current_user', '', false);
END;
$$ LANGUAGE plpgsql;

-- Create function for secure password hashing
CREATE OR REPLACE FUNCTION hash_password(password text)
RETURNS text AS $$
BEGIN
    RETURN crypt(password, gen_salt('bf', 12));
END;
$$ LANGUAGE plpgsql;

-- Create function for password verification
CREATE OR REPLACE FUNCTION verify_password(password text, hash text)
RETURNS boolean AS $$
BEGIN
    RETURN hash = crypt(password, hash);
END;
$$ LANGUAGE plpgsql;

-- Create indexes for RLS performance
CREATE INDEX IF NOT EXISTS idx_user_patient_access_rls ON user_patient_access(user_id, patient_id) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_present_illness_patient_rls ON present_illness(patient_id);

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION set_current_user(integer) TO application_role;
GRANT EXECUTE ON FUNCTION clear_current_user() TO application_role;
GRANT EXECUTE ON FUNCTION hash_password(text) TO application_role;
GRANT EXECUTE ON FUNCTION verify_password(text, text) TO application_role;

-- Security configuration comments
COMMENT ON FUNCTION create_audit_trail() IS 'Audit trigger function for comprehensive logging';
COMMENT ON FUNCTION set_current_user(integer) IS 'Set current user context for RLS policies';
COMMENT ON FUNCTION hash_password(text) IS 'Secure password hashing using bcrypt';
COMMENT ON FUNCTION verify_password(text, text) IS 'Verify password against bcrypt hash';
