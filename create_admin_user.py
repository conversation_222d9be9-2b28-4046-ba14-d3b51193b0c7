import os
import sys
from sqlmodel import Session
from auth.auth_service import AuthService
from services.database import get_engine
from config.settings import get_settings

def create_admin_user():
    # Initialize settings
    settings = get_settings()
    
    # Initialize database engine
    engine = get_engine()
    
    # Create database session
    with Session(engine) as session:
        auth_service = AuthService(session)
        
        # Create admin user
        user = auth_service.create_user(
            username="admin",
            password="admin123",
            full_name="Admin User",
            role="admin"
        )
        
        if user:
            print("✅ Admin user created successfully!")
            print(f"Username: admin")
            print(f"Password: admin123")
            print("\nYou can now log in using these credentials.")
        else:
            print("❌ Failed to create admin user")

if __name__ == "__main__":
    create_admin_user()