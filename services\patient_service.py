"""
Psychiatry EMR - Patient Service
Enhanced patient business logic with dependency injection for encryption.
"""

from typing import List, Optional, Dict, Any
from sqlmodel import Session, select, or_, text
from fuzzywuzzy import fuzz
import logging
from datetime import datetime

from models.patient import Patient, PatientData, UserPatientAccess
from security.encryption import CryptoInterface
from services.audit_service import AuditService

logger = logging.getLogger(__name__)

class PatientService:
    """Patient business logic with dependency injection for encryption"""
    
    def __init__(self, db_session: Session, current_user_id: int, crypto_service: CryptoInterface):
        self.db = db_session
        self.current_user_id = current_user_id
        self.crypto = crypto_service
        self.audit_service = AuditService(db_session, current_user_id)
    
    def create_patient(self, patient_data: PatientData) -> PatientData:
        """Create new patient with encryption and duplicate checking"""
        logger.info(f"Creating patient: {patient_data.name[:3]}*** (user: {self.current_user_id})")
        
        # Check for duplicates
        duplicates = self.find_potential_duplicates(patient_data)
        if duplicates:
            logger.warning(f"Potential duplicates found for {patient_data.name[:3]}***: {len(duplicates)}")
        
        try:
            # Create encrypted patient record
            patient = Patient(
                name_encrypted=self.crypto.encrypt(patient_data.name),
                dob=patient_data.dob,
                address_encrypted=self.crypto.encrypt(patient_data.address or ""),
                phone_encrypted=self.crypto.encrypt(patient_data.phone or ""),
                email_encrypted=self.crypto.encrypt(str(patient_data.email) if patient_data.email else ""),
                education=patient_data.education,
                occupation=patient_data.occupation,
                living_situation=patient_data.living_situation,
                updated_by=self.current_user_id
            )
            
            self.db.add(patient)
            self.db.commit()
            self.db.refresh(patient)
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to create patient: {e}")
            raise
        
        # Grant access to creating user
        try:
            access = UserPatientAccess(
                user_id=self.current_user_id,
                patient_id=patient.id,
                granted_by=self.current_user_id
            )
            self.db.add(access)
            self.db.commit()
        except Exception as e:
            logger.error(f"Failed to grant patient access: {e}")
            raise
        
        # Audit log
        self.audit_service.log_action("CREATE", "patient", patient.id, new_values={
            "name": patient_data.name[:3] + "***",  # Masked for audit
            "dob": str(patient_data.dob)
        })
        
        logger.info(f"Patient created successfully: ID {patient.id}")
        return self._decrypt_patient(patient)

    def get_patient(self, patient_id: int) -> Optional[PatientData]:
        """Get patient by ID with access control"""
        logger.debug(f"Retrieving patient {patient_id} (user: {self.current_user_id})")

        # Check access
        if not self._check_patient_access(patient_id):
            logger.warning(f"Access denied to patient {patient_id} for user {self.current_user_id}")
            self.audit_service.log_action("ACCESS_DENIED", "patient", patient_id)
            return None

        patient = self.db.get(Patient, patient_id)
        if not patient or not patient.is_active:
            return None

        # Audit successful access
        self.audit_service.log_action("READ", "patient", patient_id)

        return self._decrypt_patient(patient)

    def search_patients(self, search_term: str, page: int = 1, page_size: int = 25) -> Dict[str, Any]:
        """Search patients with pagination and server-side filtering"""
        logger.debug(f"Searching patients: '{search_term[:3]}***' (user: {self.current_user_id})")

        # Get accessible patient IDs
        accessible_ids = self._get_accessible_patient_ids()

        if not accessible_ids:
            return {"patients": [], "total": 0, "page": page, "page_size": page_size}

        # Calculate offset
        offset = (page - 1) * page_size

        # Use PostgreSQL text search for encrypted data (if using pg_trgm extension)
        # For now, we'll fetch and filter in memory but with pagination
        statement = select(Patient).where(
            Patient.id.in_(accessible_ids),
            Patient.is_active == True
        ).offset(offset).limit(page_size)

        patients = self.db.exec(statement).all()

        # Count total for pagination
        count_statement = select(Patient).where(
            Patient.id.in_(accessible_ids),
            Patient.is_active == True
        )
        total_count = len(self.db.exec(count_statement).all())

        # Decrypt and filter by search term
        results = []
        for patient in patients:
            try:
                decrypted = self._decrypt_patient(patient)
                if self._matches_search_term(decrypted, search_term):
                    results.append(decrypted)
            except Exception as e:
                logger.error(f"Failed to decrypt patient {patient.id} during search: {e}")
                # Continue with other patients rather than failing entire search
                continue

        # Audit search
        self.audit_service.log_action("SEARCH", "patient", None, new_values={
            "search_term": search_term[:3] + "***",
            "results_count": len(results),
            "page": page
        })

        return {
            "patients": results,
            "total": total_count,
            "page": page,
            "page_size": page_size,
            "total_pages": (total_count + page_size - 1) // page_size
        }

    def find_potential_duplicates(self, patient_data: PatientData) -> List[Dict[str, Any]]:
        """Find potential duplicate patients using fuzzy matching"""
        logger.debug(f"Checking for duplicates: {patient_data.name[:3]}***")

        # Get accessible patients
        accessible_ids = self._get_accessible_patient_ids()
        if not accessible_ids:
            return []

        # Fetch active patients
        statement = select(Patient).where(
            Patient.id.in_(accessible_ids),
            Patient.is_active == True,
            Patient.dob == patient_data.dob  # Exact DOB match for efficiency
        )
        candidates = self.db.exec(statement).all()

        duplicates = []
        for candidate in candidates:
            try:
                decrypted = self._decrypt_patient(candidate)

                # Calculate name similarity
                name_similarity = fuzz.ratio(
                    patient_data.name.lower(),
                    decrypted.name.lower()
                )

                # High similarity threshold for duplicates
                if name_similarity >= 85:
                    duplicates.append({
                        'id': candidate.id,
                        'name': decrypted.name,
                        'dob': str(decrypted.dob),
                        'similarity_score': name_similarity,
                        'created_at': candidate.created_at
                    })
            except Exception as e:
                logger.error(f"Failed to decrypt candidate {candidate.id}: {e}")
                continue

        # Sort by similarity score
        duplicates.sort(key=lambda x: x['similarity_score'], reverse=True)

        if duplicates:
            logger.info(f"Found {len(duplicates)} potential duplicates")

        return duplicates

    def merge_patients(self, primary_id: int, duplicate_id: int) -> bool:
        """Merge patients by moving clinical data and marking duplicate inactive"""
        logger.info(f"Merging patients: {duplicate_id} -> {primary_id} (user: {self.current_user_id})")

        # Verify access to both patients
        if not (self._check_patient_access(primary_id) and self._check_patient_access(duplicate_id)):
            logger.error(f"Access denied for patient merge operation")
            return False

        try:
            # Get both patients
            primary_patient = self.db.get(Patient, primary_id)
            duplicate_patient = self.db.get(Patient, duplicate_id)

            if not primary_patient or not duplicate_patient:
                logger.error(f"One or both patients not found for merge")
                return False

            # Move clinical assessments to primary patient
            from models.clinical import PresentIllness
            assessments = self.db.exec(
                select(PresentIllness).where(PresentIllness.patient_id == duplicate_id)
            ).all()

            for assessment in assessments:
                assessment.patient_id = primary_id
                assessment.updated_by = self.current_user_id

            # Move user access records
            access_records = self.db.exec(
                select(UserPatientAccess).where(UserPatientAccess.patient_id == duplicate_id)
            ).all()

            for access in access_records:
                # Check if primary patient already has access for this user
                existing_access = self.db.exec(
                    select(UserPatientAccess).where(
                        UserPatientAccess.user_id == access.user_id,
                        UserPatientAccess.patient_id == primary_id
                    )
                ).first()

                if not existing_access:
                    access.patient_id = primary_id
                else:
                    # Mark duplicate access as inactive
                    access.is_active = False

            # Mark duplicate as inactive and track merge
            duplicate_patient.is_active = False
            duplicate_patient.merged_into = primary_id
            duplicate_patient.updated_by = self.current_user_id

            self.db.commit()

            # Comprehensive audit
            self.audit_service.log_patient_merge(
                primary_id, duplicate_id, success=True
            )

            logger.info(f"Patient merge completed successfully")
            return True

        except Exception as e:
            self.db.rollback()
            logger.error(f"Patient merge failed: {e}")
            self.audit_service.log_patient_merge(
                primary_id, duplicate_id, success=False, error_message=str(e)
            )
            return False

    def _decrypt_patient(self, patient: Patient) -> PatientData:
        """Decrypt patient data for application use - DO NOT swallow exceptions"""
        try:
            email_decrypted = self.crypto.decrypt(patient.email_encrypted or "")
            return PatientData(
                id=patient.id,
                name=self.crypto.decrypt(patient.name_encrypted),
                dob=patient.dob,
                address=self.crypto.decrypt(patient.address_encrypted or ""),
                phone=self.crypto.decrypt(patient.phone_encrypted or ""),
                email=email_decrypted if email_decrypted else None,
                education=patient.education,
                occupation=patient.occupation,
                living_situation=patient.living_situation,
                created_at=patient.created_at
            )
        except Exception as e:
            logger.error(f"Failed to decrypt patient {patient.id}: {e}")
            # Log the decryption failure for audit
            self.audit_service.log_action(
                "DECRYPT_FAILED", "patient", patient.id,
                success=False, error_message=str(e)
            )
            # Re-raise instead of swallowing - this makes the patient row invisible
            raise

    def _check_patient_access(self, patient_id: int) -> bool:
        """Check if current user has access to patient"""
        statement = select(UserPatientAccess).where(
            UserPatientAccess.user_id == self.current_user_id,
            UserPatientAccess.patient_id == patient_id,
            UserPatientAccess.is_active == True
        )
        return self.db.exec(statement).first() is not None

    def _get_accessible_patient_ids(self) -> List[int]:
        """Get list of patient IDs accessible to current user"""
        statement = select(UserPatientAccess.patient_id).where(
            UserPatientAccess.user_id == self.current_user_id,
            UserPatientAccess.is_active == True
        )
        return list(self.db.exec(statement).all())

    def _matches_search_term(self, patient: PatientData, search_term: str) -> bool:
        """Check if patient matches search criteria"""
        search_lower = search_term.lower()
        return (
            search_lower in patient.name.lower() or
            (patient.phone and search_lower in patient.phone) or
            (patient.email and search_lower in patient.email.lower())
        )
