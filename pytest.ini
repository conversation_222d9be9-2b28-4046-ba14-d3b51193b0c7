# pytest.ini - Psychiatry EMR Test Configuration
[tool:pytest]
minversion = 7.0
testpaths = tests
python_files = test_*.py *_test.py
python_functions = test_*
python_classes = Test*

addopts =
    # Strict mode
    --strict-markers
    --strict-config

    # Coverage reporting
    --cov=.
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml:coverage.xml
    --cov-fail-under=80

    # Output formatting
    --tb=short
    --show-capture=no
    -v
    --color=yes

    # Performance
    --durations=10

markers =
    unit: Unit tests that test individual functions/methods in isolation
    integration: Integration tests that test multiple components working together
    security: Security-focused tests including encryption, access control, and audit trails
    performance: Performance and benchmark tests
    slow: Tests that take a long time to run (>5 seconds)
    database: Tests that require database access
    encryption: Tests specifically for encryption functionality
    audit: Tests for audit trail functionality
    dsm5: Tests for DSM-5-TR engine functionality
    patient: Tests for patient management functionality
    clinical: Tests for clinical assessment functionality
    compliance: Tests for HIPAA and regulatory compliance

filterwarnings =
    error
    ignore::UserWarning
    ignore::DeprecationWarning:reflex.*
    ignore::PendingDeprecationWarning
    ignore::sqlalchemy.exc.SAWarning

log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

timeout = 300
cache_dir = .pytest_cache
