# tests/conftest.py - Test configuration and fixtures
import pytest
import tempfile
import os
from pathlib import Path
from sqlmodel import Session, create_engine, SQLModel
from security.encryption import EncryptionService, generate_salt
from config.settings import Settings
from datetime import date

@pytest.fixture(scope="session")
def test_settings():
    """Test settings with secure defaults"""
    return Settings(
        database_url="sqlite:///test.db",
        encryption_salt=generate_salt().hex(),
        secret_key="test_secret_key_32_characters_long",
        debug_mode=True,
        log_level="DEBUG"
    )

@pytest.fixture(scope="session")
def test_crypto_service():
    """Test encryption service"""
    salt = generate_salt()
    return EncryptionService("test_password", salt)

@pytest.fixture
def test_db(test_settings):
    """Test database session"""
    engine = create_engine(test_settings.database_url, echo=False)
    
    # Import all models to ensure they're registered
    from models.patient import Patient, UserPatientAccess
    from models.clinical import PresentIllness
    from models.audit import AuditLog
    
    SQLModel.metadata.create_all(engine)
    
    with Session(engine) as session:
        yield session
    
    # Cleanup
    if test_settings.database_url.startswith("sqlite"):
        db_path = test_settings.database_url.replace("sqlite:///", "")
        if os.path.exists(db_path):
            os.remove(db_path)

@pytest.fixture
def sample_patient_data():
    """Sample patient data for testing"""
    return {
        "name": "John Doe",
        "dob": date(1990, 1, 1),
        "phone": "**********",
        "email": "<EMAIL>",
        "address": "123 Main St, Anytown, USA"
    }

@pytest.fixture
def sample_clinical_data():
    """Sample clinical assessment data for testing"""
    return {
        "patient_id": 1,
        "assessment_date": date.today(),
        "chief_complaint": "Feeling depressed for 2 weeks",
        "history_present_illness": "Patient reports persistent depressed mood, loss of interest in activities, sleep disturbances, and decreased appetite for the past 2 weeks.",
        "primary_diagnosis": "296.22",
        "secondary_diagnoses": "300.02",
        "treatment_plan": "Start SSRI medication, refer to therapy, follow-up in 2 weeks"
    }

@pytest.fixture
def dsm5_responses_mdd():
    """Sample DSM-5 responses for Major Depressive Disorder"""
    return {
        'mdd_1': True,  # Depressed mood
        'mdd_2': True,  # Anhedonia
        'mdd_3': True,  # Weight changes
        'mdd_4': True,  # Sleep changes
        'mdd_5': True,  # Psychomotor changes
        'mdd_6': False, # Fatigue
        'mdd_7': False, # Guilt
        'mdd_8': False, # Concentration
        'mdd_9': False  # Suicidal ideation
    }

@pytest.fixture
def test_user_id():
    """Test user ID for services"""
    return 1

@pytest.fixture
def test_user(test_db):
    """Create test user"""
    from models.user import User
    user = User(
        username="test_clinician",
        password_hash="hashed_password_123",
        full_name="Dr. Test Clinician",
        email="<EMAIL>",
        role="clinician",
        is_active=True,
        is_verified=True
    )
    test_db.add(user)
    test_db.commit()
    test_db.refresh(user)
    return user

@pytest.fixture
def audit_service(test_db, test_user):
    """Create audit service for testing"""
    from services.audit_service import AuditService
    return AuditService(test_db, test_user.id)

@pytest.fixture
def patient_service(test_db, test_user, test_crypto_service, audit_service):
    """Create patient service for testing"""
    from services.patient_service import PatientService
    return PatientService(test_db, audit_service, test_user.id, test_crypto_service)

@pytest.fixture
def clinical_service(test_db, test_user, audit_service):
    """Create clinical service for testing"""
    from services.clinical_service import ClinicalService
    return ClinicalService(test_db, audit_service, test_user.id)

@pytest.fixture
def dsm5_engine():
    """Create DSM-5 engine for testing"""
    from services.dsm5_engine import DSM5RuleEngine
    # Create temporary criteria file for testing
    test_criteria_file = Path("test_dsm5_criteria.yaml")

    engine = DSM5RuleEngine(str(test_criteria_file))

    yield engine

    # Cleanup
    if test_criteria_file.exists():
        test_criteria_file.unlink()

# Test utilities
class TestDataFactory:
    """Factory for creating test data"""

    @staticmethod
    def create_patient_data(**kwargs):
        """Create patient data with optional overrides"""
        from models.patient import PatientData
        defaults = {
            "first_name": "Test",
            "last_name": "Patient",
            "date_of_birth": date(1990, 1, 1),
            "gender": "Other",
            "phone_number": "************",
            "email": "<EMAIL>"
        }
        defaults.update(kwargs)
        return PatientData(**defaults)

    @staticmethod
    def create_assessment_data(patient_id: int, **kwargs):
        """Create assessment data with optional overrides"""
        from models.clinical import PresentIllnessData
        defaults = {
            "patient_id": patient_id,
            "assessment_date": date.today(),
            "chief_complaint": "Test complaint",
            "history_present_illness": "Test history",
            "primary_diagnosis": "Test diagnosis",
            "treatment_plan": "Test treatment plan"
        }
        defaults.update(kwargs)
        return PresentIllnessData(**defaults)

# Test markers
pytest.mark.unit = pytest.mark.unit
pytest.mark.integration = pytest.mark.integration
pytest.mark.security = pytest.mark.security
pytest.mark.performance = pytest.mark.performance

# Test configuration
def pytest_configure(config):
    """Configure pytest with custom markers"""
    config.addinivalue_line("markers", "unit: Unit tests")
    config.addinivalue_line("markers", "integration: Integration tests")
    config.addinivalue_line("markers", "security: Security tests")
    config.addinivalue_line("markers", "performance: Performance tests")
