"""
Psychiatry EMR - Navigation Components
Global navigation, error handling, and state integration.
"""

import reflex as rx
from typing import List, Dict, Any

from states.auth_state import AuthState
from states.patient_state import PatientState
from states.clinical_state import ClinicalState

def main_layout(page_content: rx.Component) -> rx.Component:
    """Main application layout with navigation and error handling"""
    return rx.vstack(
        # Global navigation bar
        main_navigation_bar(),
        
        # Toast notifications
        toast_notifications(),
        
        # Main content area
        rx.box(
            page_content,
            width="100%",
            flex="1"
        ),
        
        # Footer
        app_footer(),
        
        width="100%",
        min_height="100vh",
        spacing="0"
    )

def main_navigation_bar() -> rx.Component:
    """Main navigation bar with user context"""
    return rx.box(
        rx.container(
            rx.hstack(
                # Logo and brand
                rx.hstack(
                    rx.icon("brain-circuit", size=32, color="blue.600"),
                    rx.heading("Psychiatry EMR", size="6", color="blue.600"),
                    spacing="3"
                ),
                
                # Main navigation menu
                rx.cond(
                    AuthState.is_authenticated,
                    rx.hstack(
                        navigation_link("Dashboard", "/dashboard", "home"),
                        navigation_link("Patients", "/patients", "users"),
                        navigation_link("Assessments", "/assessments", "clipboard-list"),
                        navigation_link("Reports", "/reports", "bar-chart"),
                        rx.cond(
                            AuthState.is_admin,
                            navigation_link("Admin", "/admin", "settings")
                        ),
                        spacing="6"
                    )
                ),
                
                rx.spacer(),
                
                # User menu and actions
                rx.cond(
                    AuthState.is_authenticated,
                    user_menu_component(),
                    login_button_component()
                ),
                
                width="100%",
                align="center"
            ),
            max_width="1200px"
        ),
        width="100%",
        bg="white",
        border_bottom="1px solid",
        border_color="gray.200",
        p=4,
        position="sticky",
        top="0",
        z_index="1000"
    )

def navigation_link(label: str, href: str, icon: str) -> rx.Component:
    """Navigation link with active state"""
    return rx.link(
        rx.hstack(
            rx.icon(icon, size=18),
            rx.text(label),
            spacing="2"
        ),
        href=href,
        color="gray.700",
        font_weight="medium",
        _hover={"color": "blue.600"},
        _active={"color": "blue.600", "font_weight": "bold"}
    )

def user_menu_component() -> rx.Component:
    """User menu dropdown"""
    return rx.hstack(
        # Quick actions
        rx.button(
            rx.icon("plus", size=16),
            title="New Patient",
            variant="ghost",
            size="2"
        ),
        rx.button(
            rx.icon("clipboard-plus", size=16),
            title="New Assessment",
            variant="ghost",
            size="2"
        ),
        
        # User dropdown
        rx.menu.root(
            rx.menu.trigger(
                rx.button(
                    rx.hstack(
                        rx.cond(
                            AuthState.current_user,
                            AuthState.current_user.full_name,
                            "User"
                        ),
                        rx.icon("chevron-down", size=16),
                        spacing="2"
                    ),
                    variant="ghost",
                    size="2"
                )
            ),
            rx.menu.content(
                rx.menu.item(
                    rx.hstack(
                        rx.icon("user", size=16),
                        rx.text("Profile"),
                        spacing="2"
                    )
                ),
                rx.menu.item(
                    rx.hstack(
                        rx.icon("key", size=16),
                        rx.text("Change Password"),
                        spacing="2"
                    )
                ),
                rx.menu.item(
                    rx.hstack(
                        rx.icon("settings", size=16),
                        rx.text("Settings"),
                        spacing="2"
                    )
                ),
                rx.menu.separator(),
                rx.menu.item(
                    rx.hstack(
                        rx.icon("log-out", size=16),
                        rx.text("Logout"),
                        spacing="2"
                    ),
                    on_click=AuthState.logout,
                    color="red.600"
                )
            )
        ),
        
        spacing="2",
        align="center"
    )

def login_button_component() -> rx.Component:
    """Login button for unauthenticated users"""
    return rx.button(
        "Login",
        on_click=rx.redirect("/login"),
        color_scheme="blue",
        size="2"
    )

def toast_notifications() -> rx.Component:
    """Global toast notification system"""
    return rx.vstack(
        # Success messages
        rx.cond(
            PatientState.success_message,
            rx.card(
                rx.hstack(
                    rx.icon("check-circle", color="green.500"),
                    rx.vstack(
                        rx.text("Success", font_weight="bold"),
                        rx.text(PatientState.success_message),
                        spacing="1",
                        align="start"
                    ),
                    spacing="2"
                ),
                position="fixed",
                top="20px",
                right="20px",
                z_index="9999",
                max_width="400px",
                bg="green.50",
                border="1px solid",
                border_color="green.200"
            )
        ),

        # Error messages
        rx.cond(
            PatientState.error_message,
            rx.card(
                rx.hstack(
                    rx.icon("x-circle", color="red.500"),
                    rx.vstack(
                        rx.text("Error", font_weight="bold"),
                        rx.text(PatientState.error_message),
                        spacing="1",
                        align="start"
                    ),
                    spacing="2"
                ),
                position="fixed",
                top="20px",
                right="20px",
                z_index="9999",
                max_width="400px",
                bg="red.50",
                border="1px solid",
                border_color="red.200"
            )
        ),

        # Clinical state messages
        rx.cond(
            ClinicalState.success_message,
            rx.card(
                rx.hstack(
                    rx.icon("check-circle", color="green.500"),
                    rx.vstack(
                        rx.text("Success", font_weight="bold"),
                        rx.text(ClinicalState.success_message),
                        spacing="1",
                        align="start"
                    ),
                    spacing="2"
                ),
                position="fixed",
                top="80px",
                right="20px",
                z_index="9999",
                max_width="400px",
                bg="green.50",
                border="1px solid",
                border_color="green.200"
            )
        ),

        rx.cond(
            ClinicalState.error_message,
            rx.card(
                rx.hstack(
                    rx.icon("x-circle", color="red.500"),
                    rx.vstack(
                        rx.text("Error", font_weight="bold"),
                        rx.text(ClinicalState.error_message),
                        spacing="1",
                        align="start"
                    ),
                    spacing="2"
                ),
                position="fixed",
                top="80px",
                right="20px",
                z_index="9999",
                max_width="400px",
                bg="red.50",
                border="1px solid",
                border_color="red.200"
            )
        ),
        
        spacing="2"
    )

def loading_overlay() -> rx.Component:
    """Global loading overlay"""
    return rx.cond(
        PatientState.is_loading | ClinicalState.is_loading,
        rx.box(
            rx.vstack(
                rx.spinner(size="xl", color="blue.500"),
                rx.text("Loading...", font_weight="bold", color="blue.600"),
                spacing="3",
                align="center"
            ),
            position="fixed",
            top="0",
            left="0",
            width="100%",
            height="100%",
            bg="rgba(0, 0, 0, 0.5)",
            display="flex",
            align_items="center",
            justify_content="center",
            z_index="9999"
        )
    )

def breadcrumb_navigation(items: List[Dict[str, str]]) -> rx.Component:
    """Breadcrumb navigation component"""
    return rx.hstack(
        rx.foreach(
            items,
            lambda item, index: rx.hstack(
                rx.cond(
                    index > 0,
                    rx.text("/", color="gray.400")
                ),
                rx.cond(
                    item.get("href"),
                    rx.link(item["label"], href=item["href"], color="blue.600"),
                    rx.text(item["label"], font_weight="bold")
                ),
                spacing="2"
            )
        ),
        spacing="1",
        align="center"
    )

def error_boundary(content: rx.Component, error_message: str = "An error occurred") -> rx.Component:
    """Error boundary wrapper for components"""
    return rx.error_boundary(
        content,
        fallback=rx.card(
            rx.vstack(
                rx.icon("alert-triangle", size=48, color="red.500"),
                rx.heading("Something went wrong", size="5", color="red.600"),
                rx.text(error_message, color="gray.600"),
                rx.button(
                    "Reload Page",
                    on_click=rx.window_reload,
                    color_scheme="red",
                    size="2"
                ),
                spacing="3",
                align="center"
            ),
            p=6,
            text_align="center",
            border="1px solid",
            border_color="red.200",
            bg="red.50"
        )
    )

def app_footer() -> rx.Component:
    """Application footer"""
    return rx.box(
        rx.container(
            rx.hstack(
                rx.text("© 2024 Psychiatry EMR. All rights reserved.", color="gray.600", font_size="sm"),
                rx.spacer(),
                rx.hstack(
                    rx.text("Version 1.0.0", color="gray.500", font_size="xs"),
                    rx.text("•", color="gray.400"),
                    rx.link("Privacy Policy", href="/privacy", color="gray.500", font_size="xs"),
                    rx.text("•", color="gray.400"),
                    rx.link("Support", href="/support", color="gray.500", font_size="xs"),
                    spacing="2"
                ),
                width="100%",
                align="center"
            ),
            max_width="1200px"
        ),
        width="100%",
        bg="gray.100",
        border_top="1px solid",
        border_color="gray.200",
        p=4,
        mt="auto"
    )

def protected_route(content: rx.Component) -> rx.Component:
    """Protected route wrapper that requires authentication"""
    return rx.cond(
        AuthState.is_authenticated,
        content,
        rx.vstack(
            rx.heading("Authentication Required", size="6"),
            rx.text("Please log in to access this page."),
            rx.button(
                "Go to Login",
                on_click=rx.redirect("/login"),
                color_scheme="blue"
            ),
            spacing="4",
            align="center",
            p=8
        )
    )

def admin_route(content: rx.Component) -> rx.Component:
    """Admin route wrapper that requires admin privileges"""
    return rx.cond(
        AuthState.is_authenticated,
        rx.cond(
            AuthState.is_admin,
            content,
            rx.vstack(
                rx.icon("shield-x", size=48, color="red.500"),
                rx.heading("Access Denied", size="6", color="red.600"),
                rx.text("You don't have permission to access this page.", color="gray.600"),
                rx.button(
                    "Go to Dashboard",
                    on_click=rx.redirect("/dashboard"),
                    color_scheme="blue"
                ),
                spacing="4",
                align="center",
                p=8
            )
        ),
        rx.vstack(
            rx.heading("Authentication Required", size="6"),
            rx.text("Please log in to access this page."),
            rx.button(
                "Go to Login",
                on_click=rx.redirect("/login"),
                color_scheme="blue"
            ),
            spacing="4",
            align="center",
            p=8
        )
    )
