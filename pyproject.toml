# pyproject.toml
[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "psychiatry-emr"
version = "1.0.0"
description = "HIPAA-compliant Psychiatry Electronic Medical Records system with DSM-5-TR integration"
authors = [
    {name = "Psychiatry EMR Team", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.9"
dependencies = [
    "reflex>=0.4.0",
    "sqlmodel>=0.0.14",
    "alembic>=1.12.0",
    "cryptography>=41.0.0",
    "pydantic>=2.0.0",
    "pydantic-settings>=2.0.0",
    "psycopg2-binary>=2.9.0",
    "python-multipart>=0.0.6",
    "pyyaml>=6.0",
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    "python-dotenv>=1.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-benchmark>=4.0.0",
    "pytest-mock>=3.11.0",
    "black>=23.0.0",
    "ruff>=0.1.0",
    "mypy>=1.5.0",
    "pre-commit>=3.4.0",
    "faker>=19.0.0",
    "psutil>=5.9.0",
]

[tool.black]
line-length = 100
target-version = ['py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.ruff]
line-length = 100
target-version = "py39"
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "S",  # flake8-bandit (security)
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
    "N",  # pep8-naming
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "S101",  # use of assert detected (common in tests)
    "S603",  # subprocess call: check for execution of untrusted input
    "S607",  # starting a process with a partial executable path
]

[tool.ruff.per-file-ignores]
"tests/*" = ["S101", "S106"]  # Allow asserts and hardcoded passwords in tests
"scripts/*" = ["S602", "S603"]  # Allow subprocess calls in scripts

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 100
known_first_party = [
    "models",
    "services",
    "security",
    "config",
    "states",
    "pages",
    "components"
]

# MyPy configuration
[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
show_error_codes = true

[[tool.mypy.overrides]]
module = [
    "reflex.*",
    "sqlmodel.*",
    "alembic.*",
    "faker.*",
    "psutil.*",
    "pytest_benchmark.*",
]
ignore_missing_imports = true

[[tool.mypy.overrides]]
module = "tests.*"
disallow_untyped_defs = false
disallow_incomplete_defs = false

# Pytest configuration
[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=.",
    "--cov-report=term-missing",
    "--cov-report=html:htmlcov",
    "--cov-report=xml",
    "--cov-fail-under=80",
]
testpaths = ["tests"]
markers = [
    "unit: Unit tests",
    "integration: Integration tests",
    "security: Security tests",
    "performance: Performance tests",
    "slow: Slow running tests",
]

# Coverage configuration
[tool.coverage.run]
source = ["."]
omit = [
    "tests/*",
    "migrations/*",
    "scripts/*",
    "venv/*",
    ".venv/*",
    "*/site-packages/*",
    "main.py",
]
branch = true

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "raise AssertionError",
    "raise NotImplementedError",
    "if __name__ == .__main__.:",
]
show_missing = true
precision = 2
