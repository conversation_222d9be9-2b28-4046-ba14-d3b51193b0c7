# tests/performance/test_database_performance.py
import pytest
import time
from datetime import date
from models.patient import PatientData
from services.patient_service import PatientService

def test_patient_search_performance(test_db, test_crypto_service, benchmark):
    """Benchmark patient search performance"""
    service = PatientService(test_db, user_id=1, crypto_service=test_crypto_service)
    
    # Create test patients
    for i in range(100):
        patient_data = PatientData(name=f"Test Patient {i:03d}", dob=date(1990, 1, 1))
        service.create_patient(patient_data)
    
    # Benchmark search
    result = benchmark(service.search_patients, "Test")
    assert len(result["patients"]) > 0

def test_bulk_patient_creation(test_db, test_crypto_service, benchmark):
    """Benchmark bulk patient creation"""
    service = PatientService(test_db, user_id=1, crypto_service=test_crypto_service)
    
    def create_patients():
        patients = []
        for i in range(10):
            patient_data = PatientData(name=f"Bulk Patient {i}", dob=date(1990, 1, 1))
            patients.append(service.create_patient(patient_data))
        return patients
    
    result = benchmark(create_patients)
    assert len(result) == 10

def test_large_dataset_performance(test_db, test_crypto_service):
    """Test performance with large dataset"""
    service = PatientService(test_db, user_id=1, crypto_service=test_crypto_service)
    
    # Create large dataset
    print("Creating large dataset...")
    start_time = time.time()
    
    batch_size = 100
    total_patients = 1000
    
    for batch in range(0, total_patients, batch_size):
        batch_start = time.time()
        
        for i in range(batch, min(batch + batch_size, total_patients)):
            patient_data = PatientData(
                name=f"Performance Test Patient {i:04d}",
                dob=date(1990, 1, 1),
                phone=f"555{i:07d}",
                email=f"patient{i}@example.com"
            )
            service.create_patient(patient_data)
        
        batch_end = time.time()
        batch_time = batch_end - batch_start
        print(f"Batch {batch//batch_size + 1}: {batch_size} patients in {batch_time:.2f}s")
    
    creation_time = time.time() - start_time
    print(f"Created {total_patients} patients in {creation_time:.2f}s")
    print(f"Average: {creation_time/total_patients:.4f}s per patient")
    
    # Test search performance on large dataset
    search_start = time.time()
    results = service.search_patients("Performance Test", page=1, page_size=50)
    search_time = time.time() - search_start
    
    print(f"Search completed in {search_time:.4f}s")
    print(f"Found {len(results['patients'])} patients")
    
    # Performance assertions
    assert creation_time / total_patients < 0.1, "Patient creation too slow"
    assert search_time < 1.0, "Search too slow on large dataset"
    assert len(results['patients']) > 0, "Search should return results"

def test_concurrent_database_operations(test_db, test_crypto_service):
    """Test database performance under concurrent load"""
    import threading
    import queue
    
    results_queue = queue.Queue()
    num_threads = 5
    operations_per_thread = 20
    
    def database_worker(thread_id):
        service = PatientService(test_db, user_id=thread_id, crypto_service=test_crypto_service)
        thread_results = []
        
        for i in range(operations_per_thread):
            start_time = time.time()
            
            # Create patient
            patient_data = PatientData(
                name=f"Thread{thread_id} Patient{i}",
                dob=date(1990, 1, 1)
            )
            patient = service.create_patient(patient_data)
            
            # Retrieve patient
            retrieved = service.get_patient(patient.id)
            assert retrieved is not None
            
            # Search for patient
            search_results = service.search_patients(f"Thread{thread_id}")
            assert len(search_results["patients"]) > 0
            
            end_time = time.time()
            thread_results.append(end_time - start_time)
        
        results_queue.put((thread_id, thread_results))
    
    # Start threads
    threads = []
    start_time = time.time()
    
    for thread_id in range(1, num_threads + 1):
        thread = threading.Thread(target=database_worker, args=(thread_id,))
        thread.start()
        threads.append(thread)
    
    # Wait for completion
    for thread in threads:
        thread.join()
    
    end_time = time.time()
    total_time = end_time - start_time
    
    # Collect results
    all_times = []
    while not results_queue.empty():
        thread_id, thread_times = results_queue.get()
        all_times.extend(thread_times)
        avg_thread_time = sum(thread_times) / len(thread_times)
        print(f"Thread {thread_id}: avg {avg_thread_time:.4f}s per operation")
    
    # Calculate statistics
    total_operations = num_threads * operations_per_thread
    avg_time = sum(all_times) / len(all_times)
    
    print(f"Concurrent database test:")
    print(f"  Total operations: {total_operations}")
    print(f"  Total time: {total_time:.2f}s")
    print(f"  Operations per second: {total_operations / total_time:.2f}")
    print(f"  Average time per operation: {avg_time:.4f}s")
    
    # Performance assertions
    assert avg_time < 0.5, f"Database operations too slow: {avg_time:.4f}s"
    assert total_operations / total_time > 10, "Database throughput too low"

def test_duplicate_detection_performance(test_db, test_crypto_service):
    """Test performance of duplicate detection"""
    service = PatientService(test_db, user_id=1, crypto_service=test_crypto_service)
    
    # Create patients with similar names
    base_names = ["John Smith", "Jane Doe", "Michael Johnson", "Sarah Wilson", "David Brown"]
    
    print("Creating patients for duplicate detection test...")
    for i, base_name in enumerate(base_names):
        for j in range(20):  # 20 variations of each name
            variations = [
                base_name,
                base_name.replace(" ", "  "),  # Extra space
                base_name.lower(),
                base_name.upper(),
                f"{base_name} Jr.",
                f"Dr. {base_name}",
            ]
            
            name = variations[j % len(variations)]
            patient_data = PatientData(
                name=name,
                dob=date(1990 + (j % 10), 1, 1)  # Vary birth years
            )
            service.create_patient(patient_data)
    
    # Test duplicate detection performance
    test_data = PatientData(name="John Smith", dob=date(1990, 1, 1))
    
    start_time = time.time()
    duplicates = service.find_potential_duplicates(test_data)
    detection_time = time.time() - start_time
    
    print(f"Duplicate detection completed in {detection_time:.4f}s")
    print(f"Found {len(duplicates)} potential duplicates")
    
    # Performance assertion
    assert detection_time < 2.0, f"Duplicate detection too slow: {detection_time:.4f}s"
    assert len(duplicates) > 0, "Should find some duplicates"

def test_pagination_performance(test_db, test_crypto_service):
    """Test pagination performance with large result sets"""
    service = PatientService(test_db, user_id=1, crypto_service=test_crypto_service)
    
    # Create patients with common search term
    print("Creating patients for pagination test...")
    for i in range(500):
        patient_data = PatientData(
            name=f"Common Name {i:03d}",
            dob=date(1990, 1, 1)
        )
        service.create_patient(patient_data)
    
    # Test different page sizes
    page_sizes = [10, 25, 50, 100]
    
    for page_size in page_sizes:
        start_time = time.time()
        
        # Get first page
        results = service.search_patients("Common Name", page=1, page_size=page_size)
        
        end_time = time.time()
        search_time = end_time - start_time
        
        print(f"Page size {page_size}: {search_time:.4f}s, {len(results['patients'])} results")
        
        # Verify pagination info
        assert results['page'] == 1
        assert results['page_size'] == page_size
        assert results['total_pages'] > 1
        assert len(results['patients']) <= page_size
        
        # Performance assertion
        assert search_time < 1.0, f"Pagination too slow for page size {page_size}"

def test_memory_usage_large_dataset(test_db, test_crypto_service):
    """Test memory usage with large dataset operations"""
    import psutil
    import os
    
    process = psutil.Process(os.getpid())
    initial_memory = process.memory_info().rss / 1024 / 1024  # MB
    
    service = PatientService(test_db, user_id=1, crypto_service=test_crypto_service)
    
    # Create and process large dataset
    for i in range(200):
        patient_data = PatientData(
            name=f"Memory Test Patient {i}",
            dob=date(1990, 1, 1),
            address=f"Address {i} with some longer text to increase memory usage",
            phone=f"555{i:07d}",
            email=f"memory.test.{i}@example.com"
        )
        patient = service.create_patient(patient_data)
        
        # Retrieve and search to exercise memory
        retrieved = service.get_patient(patient.id)
        search_results = service.search_patients(f"Memory Test")
        
        # Check memory every 50 operations
        if i % 50 == 0:
            current_memory = process.memory_info().rss / 1024 / 1024
            memory_increase = current_memory - initial_memory
            print(f"After {i} operations: {current_memory:.2f} MB (+{memory_increase:.2f} MB)")
    
    final_memory = process.memory_info().rss / 1024 / 1024
    total_increase = final_memory - initial_memory
    
    print(f"Memory usage test completed:")
    print(f"  Initial: {initial_memory:.2f} MB")
    print(f"  Final: {final_memory:.2f} MB")
    print(f"  Increase: {total_increase:.2f} MB")
    
    # Memory increase should be reasonable
    assert total_increase < 200, f"Memory usage too high: {total_increase:.2f} MB"
