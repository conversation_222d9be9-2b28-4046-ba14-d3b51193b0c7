# Psychiatry EMR Production Configuration Template
# Copy this file to .env and update the values for your environment

# REQUIRED: Generate these values and keep them secret
# Database Configuration
POSTGRES_DB=psychiatry_emr
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_secure_postgres_password_here

# Redis Configuration
REDIS_PASSWORD=your_secure_redis_password_here

# Application Database URL (will be constructed automatically in Docker)
DATABASE_URL=****************************************************************/psychiatry_emr

# Encryption Configuration (CRITICAL - Generate new values)
# Generate with: python -c "import secrets; print(secrets.token_hex(32))"
ENCRYPTION_SALT=your_64_character_hex_salt_here

# JWT Secret Key (CRITICAL - Generate new value)
# Generate with: python -c "import secrets; print(secrets.token_urlsafe(32))"
SECRET_KEY=your_32_character_secret_key_here

# Application Settings
APP_NAME=Psychiatry EMR
APP_VERSION=1.0.0
DEBUG_MODE=false
LOG_LEVEL=INFO
LOG_FILE=psychiatry_emr.log

# Security Settings
SESSION_TIMEOUT_MINUTES=30
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION_MINUTES=15
AUDIT_LOG_RETENTION_DAYS=2555

# Database Connection Pool Settings
DB_POOL_SIZE=5
DB_MAX_OVERFLOW=10

# DSM-5-TR Configuration
DSM5_CRITERIA_FILE=config/dsm5_criteria.yaml

# SSL/TLS Configuration (for production)
# SSL_CERT_PATH=/etc/ssl/certs/server.crt
# SSL_KEY_PATH=/etc/ssl/private/server.key

# Backup Configuration
# BACKUP_ENCRYPTION_KEY=your_backup_encryption_key_here
# BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM

# Monitoring and Alerting
# MONITORING_ENABLED=true
# ALERT_EMAIL=<EMAIL>
# SMTP_SERVER=smtp.yourdomain.com
# SMTP_PORT=587
# SMTP_USERNAME=<EMAIL>
# SMTP_PASSWORD=your_smtp_password_here

# Production Security Headers
# SECURE_SSL_REDIRECT=true
# SECURE_HSTS_SECONDS=31536000
# SECURE_CONTENT_TYPE_NOSNIFF=true
# SECURE_BROWSER_XSS_FILTER=true
# SECURE_REFERRER_POLICY=strict-origin-when-cross-origin
