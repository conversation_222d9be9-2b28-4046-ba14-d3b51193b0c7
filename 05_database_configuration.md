# Database Configuration & Performance

## Database Connection Management

```python
# services/database.py
from sqlmodel import Session, create_engine
from contextlib import contextmanager
from config.settings import settings
import logging

logger = logging.getLogger(__name__)

# Database connection with connection pooling
engine = create_engine(
    settings.database_url,
    echo=settings.debug_mode,
    pool_size=settings.db_pool_size,
    max_overflow=settings.db_max_overflow,
    pool_pre_ping=True,  # Verify connections before use
    pool_recycle=3600,   # Recycle connections every hour
)

@contextmanager
def get_db_session():
    """Context manager for database sessions with proper cleanup"""
    session = Session(engine)
    try:
        yield session
    except Exception as e:
        session.rollback()
        logger.error(f"Database session error: {e}")
        raise
    finally:
        session.close()

# Additional database indexes for performance
DB_INDEXES = """
-- Performance indexes for common queries
CREATE INDEX IF NOT EXISTS idx_patient_dob ON patient(dob);
CREATE INDEX IF NOT EXISTS idx_patient_created_at ON patient(created_at);
CREATE INDEX IF NOT EXISTS idx_patient_active ON patient(is_active) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_audit_user_ts ON audit_log(user_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_audit_table_record ON audit_log(table_name, record_id);

CREATE INDEX IF NOT EXISTS idx_user_access_patient ON user_patient_access(patient_id) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_user_access_user ON user_patient_access(user_id) WHERE is_active = true;

-- Partial index for active patients only (more efficient)
CREATE INDEX IF NOT EXISTS idx_patient_active_only ON patient(id) WHERE is_active = true;
"""
```

## Alembic Migration Configuration

```python
# alembic/env.py - Enhanced migration configuration
from logging.config import fileConfig
from sqlalchemy import engine_from_config, pool
from alembic import context
from sqlmodel import SQLModel

# Import all models for auto-generation
from models.patient import Patient, UserPatientAccess
from models.clinical import PresentIllness
from models.audit import AuditLog

# this is the Alembic Config object
config = context.config

# Interpret the config file for Python logging
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

target_metadata = SQLModel.metadata

def run_migrations_offline():
    """Run migrations in 'offline' mode."""
    from config.settings import settings
    
    url = settings.database_url
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()

def run_migrations_online():
    """Run migrations in 'online' mode."""
    from config.settings import settings
    
    # Override URL from settings
    config.set_main_option("sqlalchemy.url", settings.database_url)
    
    connectable = engine_from_config(
        config.get_section(config.config_ini_section),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection, 
            target_metadata=target_metadata,
            compare_type=True,
            compare_server_default=True,
        )

        with context.begin_transaction():
            context.run_migrations()

if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
```

## Enhanced Main Application Entry Point

```python
# main.py - Production-ready application startup
import reflex as rx
import logging
import os
import getpass
from pathlib import Path
import sys

from security.encryption import initialize_encryption, generate_salt
from services.database import engine, DB_INDEXES
from config.settings import settings, generate_env_template
from models import *  # Import all models for SQLModel to create tables

def setup_logging():
    """Configure comprehensive logging"""
    logging.basicConfig(
        level=getattr(logging, settings.log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
        handlers=[
            logging.FileHandler(settings.log_file),
            logging.StreamHandler()
        ]
    )
    
    # Set specific loggers
    logging.getLogger('sqlalchemy.engine').setLevel(
        logging.INFO if settings.debug_mode else logging.WARNING
    )

def initialize_database():
    """Initialize database schema and indexes"""
    logger = logging.getLogger(__name__)
    
    try:
        # Create all tables
        rx.Model.metadata.create_all(engine)
        logger.info("Database tables created/verified")
        
        # Apply performance indexes
        from sqlmodel import text
        with engine.connect() as conn:
            conn.execute(text(DB_INDEXES))
            conn.commit()
        logger.info("Performance indexes applied")
        
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        raise

def get_master_password() -> str:
    """Get master password for encryption with validation"""
    if os.getenv("MASTER_PASSWORD"):
        print("⚠️  WARNING: Using master password from environment variable")
        print("   This is not recommended for production use.")
        return os.getenv("MASTER_PASSWORD")
    
    print(f"\n🏥 {settings.app_name} v{settings.app_version}")
    print("=" * 50)
    
    while True:
        password = getpass.getpass("🔐 Enter master password for encryption: ")
        if len(password) < 8:
            print("❌ Password must be at least 8 characters long")
            continue
        
        confirm = getpass.getpass("🔐 Confirm master password: ")
        if password != confirm:
            print("❌ Passwords do not match")
            continue
        
        return password

def check_environment():
    """Check that all required environment variables are set"""
    logger = logging.getLogger(__name__)
    
    # Check if .env exists
    if not Path('.env').exists():
        print("❌ .env file not found!")
        print("📝 Creating template .env file...")
        
        with open('.env', 'w') as f:
            f.write(generate_env_template())
        
        print("✅ Template .env file created")
        print("🔧 Please review and update the values, then restart the application")
        sys.exit(1)
    
    try:
        # This will validate all required settings
        _ = settings.encryption_salt
        _ = settings.secret_key
        logger.info("Environment configuration validated")
    except Exception as e:
        logger.error(f"Environment validation failed: {e}")
        print(f"❌ Environment validation failed: {e}")
        print("🔧 Please check your .env file")
        sys.exit(1)

def initialize_application():
    """Initialize application with comprehensive setup"""
    logger = logging.getLogger(__name__)
    logger.info(f"Initializing {settings.app_name} v{settings.app_version}")
    
    try:
        # Check environment first
        check_environment()
        
        # Get master password for encryption
        master_password = get_master_password()
        
        # Initialize encryption service
        crypto_service = initialize_encryption(master_password)
        logger.info("✅ Encryption service initialized")
        
        # Initialize database
        initialize_database()
        logger.info("✅ Database initialized")
        
        # Store crypto service globally for dependency injection
        # In a real implementation, you'd use a proper DI container
        rx.State._crypto_service = crypto_service
        
        print("🚀 Application initialization complete!")
        print(f"📊 Starting server on http://localhost:3000")
        
    except KeyboardInterrupt:
        print("\n❌ Initialization cancelled by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Application initialization failed: {e}")
        print(f"❌ Initialization failed: {e}")
        sys.exit(1)

# Setup logging first
setup_logging()
logger = logging.getLogger(__name__)

# Initialize application
initialize_application()

# Create Reflex app with enhanced configuration
app = rx.App(
    state=rx.State,
    style={
        "font_family": "Inter, system-ui, sans-serif",
        "background_color": "#f8fafc",
        "color": "#1e293b",
    },
    stylesheets=[
        "https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
    ],
    theme=rx.theme(
        appearance="light",
        has_background=True,
        radius="medium",
        accent_color="blue",
    ),
)

# Configure session settings
app.state_manager.timeout = settings.session_timeout_minutes * 60

if __name__ == "__main__":
    app.run(
        host="127.0.0.1" if settings.debug_mode else "0.0.0.0",
        port=3000,
        debug=settings.debug_mode
    )
```

## SQL Performance Indexes

```sql
-- sql/performance_indexes.sql
-- Performance-optimized indexes for the Psychiatry EMR

-- Patient table indexes
CREATE INDEX IF NOT EXISTS idx_patient_dob ON patient(dob);
CREATE INDEX IF NOT EXISTS idx_patient_created_at ON patient(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_patient_active ON patient(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_patient_merged_into ON patient(merged_into) WHERE merged_into IS NOT NULL;

-- Composite index for name + DOB uniqueness checking (on encrypted data)
CREATE INDEX IF NOT EXISTS idx_patient_name_dob ON patient(name_encrypted, dob) WHERE is_active = true;

-- Audit log indexes for performance monitoring
CREATE INDEX IF NOT EXISTS idx_audit_user_ts ON audit_log(user_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_audit_table_record ON audit_log(table_name, record_id);
CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_audit_failed ON audit_log(success, timestamp DESC) WHERE success = false;

-- User access indexes
CREATE INDEX IF NOT EXISTS idx_user_access_patient ON user_patient_access(patient_id) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_user_access_user ON user_patient_access(user_id) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_user_access_granted ON user_patient_access(granted_at DESC);

-- Clinical data indexes
CREATE INDEX IF NOT EXISTS idx_present_illness_patient ON present_illness(patient_id);
CREATE INDEX IF NOT EXISTS idx_present_illness_date ON present_illness(assessment_date DESC);
CREATE INDEX IF NOT EXISTS idx_present_illness_diagnosis ON present_illness(primary_diagnosis);

-- Partial indexes for active records only (more efficient)
CREATE INDEX IF NOT EXISTS idx_patient_active_only ON patient(id, created_at) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_access_active_only ON user_patient_access(user_id, patient_id) WHERE is_active = true;

-- Text search indexes (requires pg_trgm extension)
-- These would enable better encrypted text search if implemented
-- CREATE INDEX IF NOT EXISTS idx_patient_name_trgm ON patient USING gin(name_encrypted gin_trgm_ops);

COMMENT ON INDEX idx_patient_dob IS 'Index for age-based queries and statistics';
COMMENT ON INDEX idx_audit_user_ts IS 'Primary audit query index for user activity reports';
COMMENT ON INDEX idx_patient_active_only IS 'Partial index covering most patient queries';
```

## Database Security Configuration

```sql
-- sql/setup_security.sql
-- Enhanced security setup for PostgreSQL

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS pgcrypto;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS pg_trgm;  -- For text search performance

-- Create audit trigger function
CREATE OR REPLACE FUNCTION create_audit_trail()
RETURNS TRIGGER AS $audit$
BEGIN
    -- Log all data changes
    IF TG_OP = 'DELETE' THEN
        INSERT INTO audit_log (
            user_id, table_name, record_id, action, 
            old_values, timestamp
        ) VALUES (
            COALESCE(current_setting('app.current_user', true)::int, 0),
            TG_TABLE_NAME,
            OLD.id,
            TG_OP,
            row_to_json(OLD),
            NOW()
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO audit_log (
            user_id, table_name, record_id, action,
            old_values, new_values, timestamp
        ) VALUES (
            COALESCE(current_setting('app.current_user', true)::int, 0),
            TG_TABLE_NAME,
            NEW.id,
            TG_OP,
            row_to_json(OLD),
            row_to_json(NEW),
            NOW()
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO audit_log (
            user_id, table_name, record_id, action,
            new_values, timestamp
        ) VALUES (
            COALESCE(current_setting('app.current_user', true)::int, 0),
            TG_TABLE_NAME,
            NEW.id,
            TG_OP,
            row_to_json(NEW),
            NOW()
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$audit$ LANGUAGE plpgsql;

-- Apply audit triggers to sensitive tables
CREATE TRIGGER patient_audit_trigger
    AFTER INSERT OR UPDATE OR DELETE ON patient
    FOR EACH ROW EXECUTE FUNCTION create_audit_trail();

CREATE TRIGGER user_patient_access_audit_trigger
    AFTER INSERT OR UPDATE OR DELETE ON user_patient_access
    FOR EACH ROW EXECUTE FUNCTION create_audit_trail();

-- Row Level Security policies
ALTER TABLE patient ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_patient_access ENABLE ROW LEVEL SECURITY;

-- Patients can only be accessed by authorized users
CREATE POLICY patient_access_policy ON patient
    FOR ALL TO application_role
    USING (
        EXISTS (
            SELECT 1 FROM user_patient_access upa 
            WHERE upa.user_id = COALESCE(current_setting('app.current_user', true)::int, 0)
            AND upa.patient_id = patient.id
            AND upa.is_active = true
        )
    );

-- Users can only see their own access records
CREATE POLICY user_access_policy ON user_patient_access
    FOR ALL TO application_role
    USING (user_id = COALESCE(current_setting('app.current_user', true)::int, 0));

-- Create application role
CREATE ROLE application_role;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO application_role;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO application_role;
```

## Database Configuration Notes

### Key Features

1. **Connection Pooling**: Optimized for concurrent users with proper pool management
2. **Performance Indexes**: Comprehensive indexing strategy for common query patterns
3. **Row Level Security**: Database-level access control with policies
4. **Audit Triggers**: Automatic audit trail for all data changes
5. **Migration Support**: Full Alembic integration for schema changes
6. **Security Extensions**: PostgreSQL security features enabled

### Production Considerations

- **SSL/TLS**: Always use encrypted connections in production
- **Connection Limits**: Monitor and adjust pool settings based on load
- **Index Maintenance**: Regular VACUUM and ANALYZE for optimal performance
- **Backup Strategy**: Encrypted backups with point-in-time recovery
- **Monitoring**: Database performance monitoring and alerting

### Setup Commands

```bash
# Create database with extensions
createdb psychiatry_emr
psql -d psychiatry_emr -c "CREATE EXTENSION IF NOT EXISTS pgcrypto;"
psql -d psychiatry_emr -c "CREATE EXTENSION IF NOT EXISTS pg_trgm;"

# Apply security configuration
psql -d psychiatry_emr -f sql/setup_security.sql

# Apply performance indexes
psql -d psychiatry_emr -f sql/performance_indexes.sql

# Initialize Alembic migrations
alembic init alembic
alembic stamp head
```