"""
Psychiatry EMR - User Models
User authentication and session management models.
"""

from sqlmodel import SQLModel, Field, Relationship
from typing import Optional, List, TYPE_CHECKING
from datetime import datetime
import reflex as rx

if TYPE_CHECKING:
    from models.patient import UserPatientAccess

class User(SQLModel, table=True):
    """User account model"""
    
    id: Optional[int] = Field(default=None, primary_key=True)
    username: str = Field(unique=True, index=True, max_length=50)
    password_hash: str = Field(max_length=255)
    full_name: str = Field(max_length=100)
    email: Optional[str] = Field(default=None, max_length=100)
    role: str = Field(default="clinician", max_length=20)  # clinician, admin, supervisor
    
    # Status fields
    is_active: bool = Field(default=True)
    is_verified: bool = Field(default=False)
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default=None)
    last_login: Optional[datetime] = Field(default=None)
    password_changed_at: Optional[datetime] = Field(default=None)
    
    # Relationships - temporarily commented out to avoid circular imports
    # sessions: List["UserSession"] = Relationship(back_populates="user")
    # patient_access: List["UserPatientAccess"] = Relationship(back_populates="user")

class UserSession(SQLModel, table=True):
    """User session model for authentication tracking"""
    
    id: Optional[int] = Field(default=None, primary_key=True)
    user_id: int = Field(foreign_key="user.id", index=True)
    session_token: str = Field(unique=True, index=True, max_length=255)
    
    # Session metadata
    ip_address: Optional[str] = Field(default=None, max_length=45)  # IPv6 compatible
    user_agent: Optional[str] = Field(default=None, max_length=500)
    
    # Session status
    is_active: bool = Field(default=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    last_activity: datetime = Field(default_factory=datetime.utcnow)
    expires_at: datetime
    logged_out_at: Optional[datetime] = Field(default=None)
    
    # Relationships - temporarily commented out to avoid circular imports
    # user: User = Relationship(back_populates="sessions")

# Pydantic models for API/State management
class UserData(rx.Base):
    """User data for state management"""
    
    id: int
    username: str
    full_name: str
    email: Optional[str] = None
    role: str
    is_active: bool
    created_at: datetime
    last_login: Optional[datetime] = None

class LoginRequest(rx.Base):
    """Login request data"""
    
    username: str
    password: str

class LoginResponse(rx.Base):
    """Login response data"""
    
    success: bool
    message: str
    user: Optional[UserData] = None
    session_token: Optional[str] = None
    expires_at: Optional[datetime] = None

class ChangePasswordRequest(rx.Base):
    """Change password request data"""
    
    old_password: str
    new_password: str
    confirm_password: str

class CreateUserRequest(rx.Base):
    """Create user request data"""
    
    username: str
    password: str
    full_name: str
    email: Optional[str] = None
    role: str = "clinician"
