"""
Psychiatry EMR - Audit Service
Comprehensive audit logging service for security and compliance.
"""

from sqlmodel import Session
from datetime import datetime
from typing import Optional, Dict, Any
import logging
import json

from models.audit import AuditLog

logger = logging.getLogger(__name__)

class AuditService:
    """Service for comprehensive audit logging"""
    
    def __init__(self, db_session: Session, user_id: int):
        self.db = db_session
        self.user_id = user_id
    
    def log_action(
        self,
        action: str,
        table_name: str,
        record_id: Optional[int] = None,
        old_values: Optional[Dict[str, Any]] = None,
        new_values: Optional[Dict[str, Any]] = None,
        success: bool = True,
        error_message: Optional[str] = None
    ) -> None:
        """Log an action with comprehensive details"""
        try:
            audit_log = AuditLog(
                user_id=self.user_id,
                action=action,
                table_name=table_name,
                record_id=record_id,
                old_values=json.dumps(old_values) if old_values else None,
                new_values=json.dumps(new_values) if new_values else None,
                success=success,
                error_message=error_message
            )
            
            self.db.add(audit_log)
            self.db.commit()
            
            # Log to application logs as well
            log_message = f"AUDIT: {action} on {table_name}"
            if record_id:
                log_message += f" (ID: {record_id})"
            log_message += f" by user {self.user_id}"
            
            if success:
                logger.info(log_message)
            else:
                logger.warning(f"{log_message} - FAILED: {error_message}")
                
        except Exception as e:
            # Audit logging should never break the application
            logger.error(f"Failed to write audit log: {e}")

    def log_patient_access(self, patient_id: int, action: str = "READ", success: bool = True, error_message: Optional[str] = None):
        """Convenience method for logging patient access"""
        self.log_action(
            action=action,
            table_name="patient",
            record_id=patient_id,
            success=success,
            error_message=error_message
        )

    def log_search(self, search_term: str, results_count: int, table_name: str = "patient"):
        """Convenience method for logging search operations"""
        self.log_action(
            action="SEARCH",
            table_name=table_name,
            new_values={
                "search_term": search_term[:50] + "..." if len(search_term) > 50 else search_term,
                "results_count": results_count
            }
        )

    def log_authentication(self, action: str, success: bool = True, error_message: Optional[str] = None):
        """Convenience method for logging authentication events"""
        self.log_action(
            action=action,
            table_name="authentication",
            success=success,
            error_message=error_message
        )

    def log_dsm5_evaluation(self, disorder_code: str, criteria_met: bool, confidence_level: float):
        """Convenience method for logging DSM-5-TR evaluations"""
        self.log_action(
            action="DSM5_EVAL",
            table_name="dsm5_criteria",
            new_values={
                "disorder_code": disorder_code,
                "criteria_met": criteria_met,
                "confidence_level": confidence_level
            }
        )

    def log_patient_merge(self, primary_id: int, duplicate_id: int, success: bool = True, error_message: Optional[str] = None):
        """Convenience method for logging patient merge operations"""
        self.log_action(
            action="MERGE",
            table_name="patient",
            record_id=duplicate_id,
            new_values={
                "merged_into": primary_id
            },
            success=success,
            error_message=error_message
        )

    def log_encryption_failure(self, table_name: str, record_id: Optional[int] = None, error_message: Optional[str] = None):
        """Convenience method for logging encryption/decryption failures"""
        self.log_action(
            action="DECRYPT_FAILED",
            table_name=table_name,
            record_id=record_id,
            success=False,
            error_message=error_message
        )
