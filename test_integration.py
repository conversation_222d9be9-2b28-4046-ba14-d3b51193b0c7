#!/usr/bin/env python3
"""
Integration Test Suite for Psychiatry EMR
Tests all major components and their integration.
"""

import os
import sys
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_environment_setup():
    """Test environment configuration"""
    logger.info("Testing environment setup...")
    
    # Set test environment variables
    os.environ["DATABASE_URL"] = "sqlite:///test_psychiatry_emr.db"
    os.environ["DEBUG_MODE"] = "true"
    os.environ["ENCRYPTION_SALT"] = "a1b2c3d4e5f67890********************abcdef**********abcdef123456"
    os.environ["SECRET_KEY"] = "test_secret_key_for_testing_only_32chars"
    
    try:
        from config.settings import get_settings
        settings = get_settings()
        assert settings.debug_mode == True
        assert settings.encryption_salt is not None
        logger.info("✅ Environment setup test passed")
        return True
    except Exception as e:
        logger.error(f"❌ Environment setup test failed: {e}")
        return False

def test_database_models():
    """Test database models and connections"""
    logger.info("Testing database models...")

    try:
        from models.patient import Patient
        from models.clinical import PresentIllness
        from models.user import User
        from models.audit import AuditLog

        # Test model creation
        patient = Patient(
            first_name="Test",
            last_name="Patient",
            date_of_birth="1990-01-01",
            ssn_hash="test_hash",
            phone_number="+**********",
            email="<EMAIL>"
        )

        logger.info("✅ Database models test passed")
        return True
    except Exception as e:
        logger.error(f"❌ Database models test failed: {e}")
        return False

def test_encryption_service():
    """Test encryption service"""
    logger.info("Testing encryption service...")

    try:
        from security.encryption import EncryptionService

        # Initialize with test parameters
        encryption = EncryptionService("test_master_password", "a1b2c3d4e5f67890********************abcdef**********abcdef123456")
        test_data = "Sensitive patient information"

        # Test encryption/decryption
        encrypted = encryption.encrypt_data(test_data)
        decrypted = encryption.decrypt_data(encrypted)

        assert decrypted == test_data
        logger.info("✅ Encryption service test passed")
        return True
    except Exception as e:
        logger.error(f"❌ Encryption service test failed: {e}")
        return False

def test_dsm5_engine():
    """Test DSM-5-TR engine"""
    logger.info("Testing DSM-5-TR engine...")

    try:
        from services.dsm5_engine import DSM5RuleEngine

        engine = DSM5RuleEngine()

        # Test criteria loading
        criteria = engine.get_disorder_criteria("296.23")  # Major Depressive Disorder
        assert criteria is not None

        # Test assessment
        symptoms = [
            "Depressed mood most of the day",
            "Markedly diminished interest in activities",
            "Significant weight loss",
            "Insomnia",
            "Fatigue"
        ]

        result = engine.assess_criteria("296.23", symptoms)
        assert result is not None

        logger.info("✅ DSM-5-TR engine test passed")
        return True
    except Exception as e:
        logger.error(f"❌ DSM-5-TR engine test failed: {e}")
        return False

def test_services():
    """Test business logic services"""
    logger.info("Testing services...")

    try:
        # Test service imports only (they require dependencies)
        from services import patient_service
        from services import clinical_service
        from services import audit_service

        logger.info("✅ Services test passed")
        return True
    except Exception as e:
        logger.error(f"❌ Services test failed: {e}")
        return False

def test_authentication():
    """Test authentication system"""
    logger.info("Testing authentication...")

    try:
        from auth.auth_service import AuthService

        auth_service = AuthService()

        # Test password hashing
        password = "test_password_123"
        hashed = auth_service.hash_password(password)
        assert auth_service.verify_password(password, hashed)

        logger.info("✅ Authentication test passed")
        return True
    except Exception as e:
        logger.error(f"❌ Authentication test failed: {e}")
        return False

def test_states():
    """Test Reflex states"""
    logger.info("Testing Reflex states...")

    try:
        # Test state imports only (they require Reflex app context)
        from states import auth_state
        from states import patient_state
        from states import clinical_state

        logger.info("✅ States test passed")
        return True
    except Exception as e:
        logger.error(f"❌ States test failed: {e}")
        return False

def test_pages():
    """Test page components"""
    logger.info("Testing page components...")

    try:
        # Test page imports only (they require Reflex app context)
        from pages import dashboard
        from pages import patient_search
        from pages import clinical_assessment
        from pages import login

        logger.info("✅ Pages test passed")
        return True
    except Exception as e:
        logger.error(f"❌ Pages test failed: {e}")
        return False

def test_main_application():
    """Test main application initialization"""
    logger.info("Testing main application...")

    try:
        # Test basic import without running the app
        import sys
        import importlib.util

        spec = importlib.util.spec_from_file_location("main", "main.py")
        if spec and spec.loader:
            logger.info("✅ Main application test passed")
            return True
        else:
            logger.error("❌ Main application file not found")
            return False
    except Exception as e:
        logger.error(f"❌ Main application test failed: {e}")
        return False

def run_all_tests():
    """Run all integration tests"""
    print("🏥 Psychiatry EMR - Integration Test Suite")
    print("=" * 50)
    
    tests = [
        test_environment_setup,
        test_database_models,
        test_encryption_service,
        test_dsm5_engine,
        test_services,
        test_authentication,
        test_states,
        test_pages,
        test_main_application
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            logger.error(f"Test {test.__name__} crashed: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! Application is ready for deployment.")
        return True
    else:
        print(f"❌ {failed} tests failed. Please fix issues before deployment.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
