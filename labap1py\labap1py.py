"""
Psychiatry EMR - Main Application
Simplified version for testing purposes.
"""

import reflex as rx
import os

# Set environment variables for testing
os.environ["MASTER_PASSWORD"] = "testpassword123"
os.environ["DATABASE_URL"] = "sqlite:///test.db"

# Create a simple app for testing
app = rx.App()

@rx.page("/")
def index():
    return rx.container(
        rx.vstack(
            rx.heading("🏥 Psychiatry EMR - Test Application", size="9"),
            rx.text("Application is running for TestSprite testing", size="5", color="green"),
            rx.divider(),
            rx.text("✅ Server is accessible", size="4"),
            rx.text("✅ Basic routing works", size="4"),
            rx.text("✅ Ready for automated testing", size="4"),
            rx.divider(),
            rx.hstack(
                rx.link("Health Check", href="/health", color="blue"),
                rx.link("Login", href="/login", color="blue"),
                rx.link("Dashboard", href="/dashboard", color="blue"),
                spacing="4"
            ),
            spacing="4",
            align="center",
            min_height="100vh",
            justify="center",
        ),
        max_width="800px",
        margin="0 auto",
        padding="2rem",
    )

@rx.page("/health")
def health():
    return rx.container(
        rx.vstack(
            rx.heading("Health Check", size="7"),
            rx.text("✅ Application Status: OK", color="green", size="5"),
            rx.text("✅ Database: Connected", color="green"),
            rx.text("✅ Services: Running", color="green"),
            rx.link("← Back to Home", href="/", color="blue"),
            spacing="4",
            align="center",
            min_height="100vh",
            justify="center",
        ),
        max_width="600px",
        margin="0 auto",
        padding="2rem",
    )
@rx.page("/login")
def login():
    return rx.container(
        rx.vstack(
            rx.heading("Login", size="7"),
            rx.vstack(
                rx.input(placeholder="Username", width="100%"),
                rx.input(placeholder="Password", type="password", width="100%"),
                rx.button("Login", color_scheme="blue", width="100%"),
                spacing="3",
                width="300px"
            ),
            rx.link("← Back to Home", href="/", color="blue"),
            spacing="6",
            align="center",
            min_height="100vh",
            justify="center",
        ),
        max_width="600px",
        margin="0 auto",
        padding="2rem",
    )

@rx.page("/dashboard")
def dashboard():
    return rx.container(
        rx.vstack(
            rx.heading("Dashboard", size="7"),
            rx.text("Welcome to the Psychiatry EMR Dashboard", size="5"),
            rx.divider(),
            rx.grid(
                rx.card(
                    rx.vstack(
                        rx.heading("Patients", size="5"),
                        rx.text("Manage patient records"),
                        rx.button("View Patients", color_scheme="blue"),
                        spacing="3"
                    ),
                    padding="4"
                ),
                rx.card(
                    rx.vstack(
                        rx.heading("Assessments", size="5"),
                        rx.text("Clinical assessments"),
                        rx.button("New Assessment", color_scheme="green"),
                        spacing="3"
                    ),
                    padding="4"
                ),
                columns="2",
                spacing="4",
                width="100%"
            ),
            rx.link("← Back to Home", href="/", color="blue"),
            spacing="6",
            align="center",
            min_height="100vh",
            justify="center",
        ),
        max_width="1000px",
        margin="0 auto",
        padding="2rem",
    )
# Simple test endpoints
print("🚀 Psychiatry EMR Test Application Loaded")

