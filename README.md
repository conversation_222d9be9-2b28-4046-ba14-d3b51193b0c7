# Psychiatry Patient Management System

A secure, local psychiatry EMR application built with Python Reflex, implementing DSM-5-TR criteria with intelligent forms, comprehensive patient management, and robust security features.

## Features

### Security & Privacy
- **Multi-layer encryption**: Application-level encryption with database backup
- **Master password authentication**: Required on startup, not stored
- **Comprehensive audit trail**: All actions logged with user attribution
- **Role-based access control**: Granular patient access permissions
- **HIPAA compliance considerations**: Built for healthcare privacy requirements

### Clinical Features
- **DSM-5-TR integration**: Configurable diagnostic criteria engine
- **Intelligent forms**: Dynamic forms based on diagnostic criteria
- **Patient management**: Comprehensive patient records with duplicate detection
- **Clinical assessments**: Structured present illness documentation
- **Search and reporting**: Encrypted search with performance optimization

## Quick Start

1. **Setup Environment**
   ```bash
   # Copy environment template
   cp .env.template .env
   
   # Generate secure keys (update .env with these values)
   python -c "import secrets; print('ENCRYPTION_SALT=' + secrets.token_hex(32))"
   python -c "import secrets; print('SECRET_KEY=' + secrets.token_urlsafe(32))"
   ```

2. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   pip install -r requirements-dev.txt  # For development
   ```

3. **Setup Database**
   ```bash
   # Create PostgreSQL database
   createdb psychiatry_emr
   
   # Enable extensions
   psql -d psychiatry_emr -c "CREATE EXTENSION IF NOT EXISTS pgcrypto;"
   ```

4. **Run Application**
   ```bash
   reflex run
   ```

## Technology Stack

- **Frontend/Backend**: Reflex (Python-based full-stack framework)
- **Database**: Local PostgreSQL with SQLModel ORM
- **Authentication**: Reflex built-in authentication
- **Security**: Explicit encryption + PostgreSQL pgcrypto
- **Validation**: Pydantic for all data contracts

## Development

See the development setup guide in the documentation for detailed instructions on setting up the development environment, running tests, and contributing to the project.

## Security Notice

This application handles sensitive healthcare data. Ensure proper security measures are in place before deployment:

- Use strong master passwords
- Enable database SSL/TLS
- Regular security audits
- Encrypted backups
- Network security measures

## License

This project is intended for healthcare professionals and must comply with all applicable healthcare privacy regulations including HIPAA.
