#!/bin/bash
# scripts/manage_db.sh - Database management utilities

function create_migration() {
    echo "📝 Creating new migration..."
    alembic revision --autogenerate -m "$1"
}

function apply_migrations() {
    echo "🔄 Applying database migrations..."
    alembic upgrade head
}

function rollback_migration() {
    echo "⏪ Rolling back last migration..."
    alembic downgrade -1
}

function reset_database() {
    echo "🗑️ Resetting database..."
    read -p "Are you sure? This will delete all data. (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        dropdb psychiatry_emr_dev
        createdb psychiatry_emr_dev
        psql -d psychiatry_emr_dev -c "CREATE EXTENSION IF NOT EXISTS pgcrypto;"
        psql -d psychiatry_emr_dev -c "CREATE EXTENSION IF NOT EXISTS pg_trgm;"
        psql -d psychiatry_emr_dev -f sql/performance_indexes.sql
        echo "✅ Database reset complete"
    fi
}

function backup_database() {
    echo "💾 Creating database backup..."
    mkdir -p backups
    timestamp=$(date +%Y%m%d_%H%M%S)
    pg_dump psychiatry_emr_dev > "backups/dev_backup_$timestamp.sql"
    echo "✅ Backup created: backups/dev_backup_$timestamp.sql"
}

# Command dispatcher
case "$1" in
    "migrate")
        create_migration "$2"
        ;;
    "upgrade")
        apply_migrations
        ;;
    "downgrade")
        rollback_migration
        ;;
    "reset")
        reset_database
        ;;
    "backup")
        backup_database
        ;;
    *)
        echo "Usage: $0 {migrate|upgrade|downgrade|reset|backup} [message]"
        echo ""
        echo "Commands:"
        echo "  migrate <message>  - Create new migration"
        echo "  upgrade           - Apply pending migrations"
        echo "  downgrade         - Rollback last migration"
        echo "  reset             - Reset database (WARNING: destroys data)"
        echo "  backup            - Create database backup"
        exit 1
        ;;
esac
