# Data Models & Business Services

## Enhanced Data Models with Pydantic Integration

### Patient Data Models

```python
# models/patient.py
from __future__ import annotations
import reflex as rx
from sqlmodel import Field, Relationship, UniqueConstraint
from datetime import date, datetime
from typing import Optional, List
from pydantic import BaseModel, validator, EmailStr
import logging
import re

logger = logging.getLogger(__name__)

# Pydantic models for data contracts
class PatientData(BaseModel):
    """Pydantic model for patient data validation and serialization"""
    id: Optional[int] = None
    name: str
    dob: date
    address: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[EmailStr] = None  # Use EmailStr for validation
    education: Optional[str] = None
    occupation: Optional[str] = None
    living_situation: Optional[str] = None
    created_at: Optional[datetime] = None
    
    @validator('name')
    def name_must_not_be_empty(cls, v):
        if not v or not v.strip():
            raise ValueError('Name cannot be empty')
        return v.strip()
    
    @validator('phone')
    def format_phone(cls, v):
        if not v:
            return None
        
        # Extract digits only
        digits = ''.join(filter(str.isdigit, v))
        
        # US phone number validation
        if len(digits) == 10:
            return f"({digits[:3]}) {digits[3:6]}-{digits[6:]}"
        elif len(digits) == 11 and digits[0] == '1':
            return f"+1 ({digits[1:4]}) {digits[4:7]}-{digits[7:]}"
        else:
            # Invalid phone number format
            raise ValueError('Phone number must be 10 digits (US format)')

class Patient(rx.Model, table=True):
    """Patient model with encrypted fields and unique constraints"""
    __table_args__ = (
        UniqueConstraint('name_encrypted', 'dob', name='unique_patient_name_dob'),
    )
    
    id: Optional[int] = Field(default=None, primary_key=True)
    name_encrypted: str  # Encrypted in service layer
    dob: date = Field(index=True)  # Add index for common queries
    address_encrypted: Optional[str] = Field(default=None)
    phone_encrypted: Optional[str] = Field(default=None)
    email_encrypted: Optional[str] = Field(default=None)
    education: Optional[str] = Field(default=None)
    occupation: Optional[str] = Field(default=None)
    living_situation: Optional[str] = Field(default=None)
    created_at: datetime = Field(default_factory=datetime.now, index=True)
    updated_by: Optional[int] = Field(default=None, foreign_key="user.id")
    is_active: bool = Field(default=True)  # For soft deletes
    merged_into: Optional[int] = Field(default=None, foreign_key="patient.id")  # Track merges
    
    # Relationships with explicit cascade control
    user_access: List["UserPatientAccess"] = Relationship(
        back_populates="patient",
        sa_relationship_kwargs={"cascade": "save-update, merge"}  # Safer cascade
    )
    clinical_assessments: List["PresentIllness"] = Relationship(
        back_populates="patient",
        sa_relationship_kwargs={"cascade": "save-update, merge, delete"}
    )

class UserPatientAccess(rx.Model, table=True):
    """User access control with audit trail"""
    id: Optional[int] = Field(default=None, primary_key=True)
    user_id: int = Field(foreign_key="user.id")
    patient_id: int = Field(foreign_key="patient.id")
    granted_at: datetime = Field(default_factory=datetime.now)
    granted_by: int = Field(foreign_key="user.id")
    is_active: bool = Field(default=True)
    
    # Relationships
    patient: Optional[Patient] = Relationship(back_populates="user_access")
```

### Clinical Assessment Models

```python
# models/clinical.py
from __future__ import annotations
import reflex as rx
from sqlmodel import Field, Relationship
from datetime import datetime, date
from typing import Optional, Dict, Any
from pydantic import BaseModel
import json

class PresentIllnessData(BaseModel):
    """Pydantic model for present illness validation"""
    id: Optional[int] = None
    patient_id: int
    assessment_date: date
    chief_complaint: str
    history_present_illness: str
    primary_diagnosis: Optional[str] = None
    secondary_diagnoses: Optional[str] = None
    dsm5_criteria_met: Optional[Dict[str, Any]] = None
    treatment_plan: Optional[str] = None
    created_at: Optional[datetime] = None

class PresentIllness(rx.Model, table=True):
    """Present illness documentation with DSM-5-TR integration"""
    id: Optional[int] = Field(default=None, primary_key=True)
    patient_id: int = Field(foreign_key="patient.id")
    assessment_date: date = Field(index=True)
    chief_complaint: str
    history_present_illness: str
    primary_diagnosis: Optional[str] = Field(default=None)
    secondary_diagnoses: Optional[str] = Field(default=None)
    dsm5_criteria_json: Optional[str] = Field(default=None)  # JSON string for criteria results
    treatment_plan: Optional[str] = Field(default=None)
    created_at: datetime = Field(default_factory=datetime.now)
    updated_by: int = Field(foreign_key="user.id")
    
    # Relationships
    patient: Optional["Patient"] = Relationship(back_populates="clinical_assessments")
    
    @property
    def dsm5_criteria_met(self) -> Optional[Dict[str, Any]]:
        """Parse DSM-5 criteria from JSON string"""
        if self.dsm5_criteria_json:
            return json.loads(self.dsm5_criteria_json)
        return None
    
    @dsm5_criteria_met.setter
    def dsm5_criteria_met(self, value: Optional[Dict[str, Any]]) -> None:
        """Store DSM-5 criteria as JSON string"""
        self.dsm5_criteria_json = json.dumps(value) if value else None
```

## Enhanced Patient Service with Dependency Injection

```python
# services/patient_service.py
from typing import List, Optional, Dict, Any
from sqlmodel import Session, select, or_, text
from fuzzywuzzy import fuzz
import logging
from datetime import datetime

from models.patient import Patient, PatientData, UserPatientAccess
from security.encryption import CryptoInterface
from services.audit_service import AuditService

logger = logging.getLogger(__name__)

class PatientService:
    """Patient business logic with dependency injection for encryption"""
    
    def __init__(self, db_session: Session, current_user_id: int, crypto_service: CryptoInterface):
        self.db = db_session
        self.current_user_id = current_user_id
        self.crypto = crypto_service
        self.audit_service = AuditService(db_session, current_user_id)
    
    def create_patient(self, patient_data: PatientData) -> PatientData:
        """Create new patient with encryption and duplicate checking"""
        logger.info(f"Creating patient: {patient_data.name[:3]}*** (user: {self.current_user_id})")
        
        # Check for duplicates
        duplicates = self.find_potential_duplicates(patient_data)
        if duplicates:
            logger.warning(f"Potential duplicates found for {patient_data.name[:3]}***: {len(duplicates)}")
        
        try:
            # Create encrypted patient record
            patient = Patient(
                name_encrypted=self.crypto.encrypt(patient_data.name),
                dob=patient_data.dob,
                address_encrypted=self.crypto.encrypt(patient_data.address or ""),
                phone_encrypted=self.crypto.encrypt(patient_data.phone or ""),
                email_encrypted=self.crypto.encrypt(str(patient_data.email) if patient_data.email else ""),
                education=patient_data.education,
                occupation=patient_data.occupation,
                living_situation=patient_data.living_situation,
                updated_by=self.current_user_id
            )
            
            self.db.add(patient)
            self.db.commit()
            self.db.refresh(patient)
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to create patient: {e}")
            raise
        
        # Grant access to creating user
        try:
            access = UserPatientAccess(
                user_id=self.current_user_id,
                patient_id=patient.id,
                granted_by=self.current_user_id
            )
            self.db.add(access)
            self.db.commit()
        except Exception as e:
            logger.error(f"Failed to grant patient access: {e}")
            raise
        
        # Audit log
        self.audit_service.log_action("CREATE", "patient", patient.id, new_values={
            "name": patient_data.name[:3] + "***",  # Masked for audit
            "dob": str(patient_data.dob)
        })
        
        logger.info(f"Patient created successfully: ID {patient.id}")
        return self._decrypt_patient(patient)
    
    def get_patient(self, patient_id: int) -> Optional[PatientData]:
        """Get patient by ID with access control"""
        logger.debug(f"Retrieving patient {patient_id} (user: {self.current_user_id})")
        
        # Check access
        if not self._check_patient_access(patient_id):
            logger.warning(f"Access denied to patient {patient_id} for user {self.current_user_id}")
            self.audit_service.log_action("ACCESS_DENIED", "patient", patient_id)
            return None
        
        patient = self.db.get(Patient, patient_id)
        if not patient or not patient.is_active:
            return None
        
        # Audit successful access
        self.audit_service.log_action("READ", "patient", patient_id)
        
        return self._decrypt_patient(patient)
    
    def search_patients(self, search_term: str, page: int = 1, page_size: int = 25) -> Dict[str, Any]:
        """Search patients with pagination and server-side filtering"""
        logger.debug(f"Searching patients: '{search_term[:3]}***' (user: {self.current_user_id})")
        
        # Get accessible patient IDs
        accessible_ids = self._get_accessible_patient_ids()
        
        if not accessible_ids:
            return {"patients": [], "total": 0, "page": page, "page_size": page_size}
        
        # Calculate offset
        offset = (page - 1) * page_size
        
        # Use PostgreSQL text search for encrypted data (if using pg_trgm extension)
        # For now, we'll fetch and filter in memory but with pagination
        statement = select(Patient).where(
            Patient.id.in_(accessible_ids),
            Patient.is_active == True
        ).offset(offset).limit(page_size)
        
        patients = self.db.exec(statement).all()
        
        # Count total for pagination
        count_statement = select(Patient).where(
            Patient.id.in_(accessible_ids),
            Patient.is_active == True
        )
        total_count = len(self.db.exec(count_statement).all())
        
        # Decrypt and filter by search term
        results = []
        for patient in patients:
            try:
                decrypted = self._decrypt_patient(patient)
                if self._matches_search_term(decrypted, search_term):
                    results.append(decrypted)
            except Exception as e:
                logger.error(f"Failed to decrypt patient {patient.id} during search: {e}")
                # Continue with other patients rather than failing entire search
                continue
        
        # Audit search
        self.audit_service.log_action("SEARCH", "patient", None, new_values={
            "search_term": search_term[:3] + "***",
            "results_count": len(results),
            "page": page
        })
        
        return {
            "patients": results,
            "total": total_count,
            "page": page,
            "page_size": page_size,
            "total_pages": (total_count + page_size - 1) // page_size
        }
    
    def merge_patients(self, primary_id: int, duplicate_id: int) -> bool:
        """Merge patients by moving clinical data and marking duplicate inactive"""
        logger.info(f"Merging patients: {duplicate_id} -> {primary_id} (user: {self.current_user_id})")
        
        # Verify access to both patients
        if not (self._check_patient_access(primary_id) and self._check_patient_access(duplicate_id)):
            logger.error(f"Access denied for patient merge operation")
            return False
        
        try:
            # Get both patients
            primary_patient = self.db.get(Patient, primary_id)
            duplicate_patient = self.db.get(Patient, duplicate_id)
            
            if not primary_patient or not duplicate_patient:
                logger.error(f"One or both patients not found for merge")
                return False
            
            # Move clinical assessments to primary patient
            from models.clinical import PresentIllness
            assessments = self.db.exec(
                select(PresentIllness).where(PresentIllness.patient_id == duplicate_id)
            ).all()
            
            for assessment in assessments:
                assessment.patient_id = primary_id
                assessment.updated_by = self.current_user_id
            
            # Move user access records
            access_records = self.db.exec(
                select(UserPatientAccess).where(UserPatientAccess.patient_id == duplicate_id)
            ).all()
            
            for access in access_records:
                # Check if primary patient already has access for this user
                existing_access = self.db.exec(
                    select(UserPatientAccess).where(
                        UserPatientAccess.user_id == access.user_id,
                        UserPatientAccess.patient_id == primary_id
                    )
                ).first()
                
                if not existing_access:
                    access.patient_id = primary_id
                else:
                    # Mark duplicate access as inactive
                    access.is_active = False
            
            # Mark duplicate as inactive and track merge
            duplicate_patient.is_active = False
            duplicate_patient.merged_into = primary_id
            duplicate_patient.updated_by = self.current_user_id
            
            self.db.commit()
            
            # Comprehensive audit
            self.audit_service.log_action("MERGE", "patient", duplicate_id, new_values={
                "merged_into": primary_id,
                "assessments_moved": len(assessments),
                "access_records_moved": len(access_records)
            })
            
            logger.info(f"Patient merge completed successfully")
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Patient merge failed: {e}")
            self.audit_service.log_action(
                "MERGE", "patient", duplicate_id, 
                success=False, error_message=str(e)
            )
            return False
    
    def find_potential_duplicates(self, patient_data: PatientData) -> List[Dict[str, Any]]:
        """Find potential duplicate patients using fuzzy matching"""
        logger.debug(f"Checking for duplicates: {patient_data.name[:3]}***")
        
        # Get accessible patients
        accessible_ids = self._get_accessible_patient_ids()
        if not accessible_ids:
            return []
        
        # Fetch active patients
        statement = select(Patient).where(
            Patient.id.in_(accessible_ids),
            Patient.is_active == True,
            Patient.dob == patient_data.dob  # Exact DOB match for efficiency
        )
        candidates = self.db.exec(statement).all()
        
        duplicates = []
        for candidate in candidates:
            try:
                decrypted = self._decrypt_patient(candidate)
                
                # Calculate name similarity
                name_similarity = fuzz.ratio(
                    patient_data.name.lower(),
                    decrypted.name.lower()
                )
                
                # High similarity threshold for duplicates
                if name_similarity >= 85:
                    duplicates.append({
                        'id': candidate.id,
                        'name': decrypted.name,
                        'dob': str(decrypted.dob),
                        'similarity_score': name_similarity,
                        'created_at': candidate.created_at
                    })
            except Exception as e:
                logger.error(f"Failed to decrypt candidate {candidate.id}: {e}")
                continue
        
        # Sort by similarity score
        duplicates.sort(key=lambda x: x['similarity_score'], reverse=True)
        
        if duplicates:
            logger.info(f"Found {len(duplicates)} potential duplicates")
        
        return duplicates
    
    def _decrypt_patient(self, patient: Patient) -> PatientData:
        """Decrypt patient data for application use - DO NOT swallow exceptions"""
        try:
            email_decrypted = self.crypto.decrypt(patient.email_encrypted or "")
            return PatientData(
                id=patient.id,
                name=self.crypto.decrypt(patient.name_encrypted),
                dob=patient.dob,
                address=self.crypto.decrypt(patient.address_encrypted or ""),
                phone=self.crypto.decrypt(patient.phone_encrypted or ""),
                email=email_decrypted if email_decrypted else None,
                education=patient.education,
                occupation=patient.occupation,
                living_situation=patient.living_situation,
                created_at=patient.created_at
            )
        except Exception as e:
            logger.error(f"Failed to decrypt patient {patient.id}: {e}")
            # Log the decryption failure for audit
            self.audit_service.log_action(
                "DECRYPT_FAILED", "patient", patient.id,
                success=False, error_message=str(e)
            )
            # Re-raise instead of swallowing - this makes the patient row invisible
            raise
    
    def _check_patient_access(self, patient_id: int) -> bool:
        """Check if current user has access to patient"""
        statement = select(UserPatientAccess).where(
            UserPatientAccess.user_id == self.current_user_id,
            UserPatientAccess.patient_id == patient_id,
            UserPatientAccess.is_active == True
        )
        return self.db.exec(statement).first() is not None
    
    def _get_accessible_patient_ids(self) -> List[int]:
        """Get list of patient IDs accessible to current user"""
        statement = select(UserPatientAccess.patient_id).where(
            UserPatientAccess.user_id == self.current_user_id,
            UserPatientAccess.is_active == True
        )
        return list(self.db.exec(statement).all())
    
    def _matches_search_term(self, patient: PatientData, search_term: str) -> bool:
        """Check if patient matches search criteria"""
        search_lower = search_term.lower()
        return (
            search_lower in patient.name.lower() or
            (patient.phone and search_lower in patient.phone) or
            (patient.email and search_lower in patient.email.lower())
        )
```

## Clinical Assessment Service

```python
# services/clinical_service.py
from typing import List, Optional, Dict, Any
from sqlmodel import Session, select
from datetime import datetime, date
import logging

from models.clinical import PresentIllness, PresentIllnessData
from models.patient import Patient
from security.encryption import CryptoInterface
from services.audit_service import AuditService
from services.dsm5_engine import DSM5RuleEngine

logger = logging.getLogger(__name__)

class ClinicalService:
    """Clinical assessment business logic with DSM-5-TR integration"""
    
    def __init__(self, db_session: Session, current_user_id: int, crypto_service: CryptoInterface):
        self.db = db_session
        self.current_user_id = current_user_id
        self.crypto = crypto_service
        self.audit_service = AuditService(db_session, current_user_id)
        self.dsm5_engine = DSM5RuleEngine()
    
    def create_assessment(self, assessment_data: PresentIllnessData, dsm5_responses: Optional[Dict[str, bool]] = None) -> PresentIllnessData:
        """Create new clinical assessment with DSM-5-TR evaluation"""
        logger.info(f"Creating assessment for patient {assessment_data.patient_id}")
        
        # Verify patient access
        if not self._check_patient_access(assessment_data.patient_id):
            logger.error(f"Access denied to patient {assessment_data.patient_id}")
            raise PermissionError("Access denied to patient")
        
        try:
            # Evaluate DSM-5 criteria if responses provided
            dsm5_results = None
            if dsm5_responses and assessment_data.primary_diagnosis:
                dsm5_results = self.dsm5_engine.evaluate_criteria(
                    assessment_data.primary_diagnosis, 
                    dsm5_responses
                )
            
            # Create assessment record
            assessment = PresentIllness(
                patient_id=assessment_data.patient_id,
                assessment_date=assessment_data.assessment_date,
                chief_complaint=assessment_data.chief_complaint,
                history_present_illness=assessment_data.history_present_illness,
                primary_diagnosis=assessment_data.primary_diagnosis,
                secondary_diagnoses=assessment_data.secondary_diagnoses,
                treatment_plan=assessment_data.treatment_plan,
                updated_by=self.current_user_id
            )
            
            # Store DSM-5 results
            if dsm5_results:
                assessment.dsm5_criteria_met = dsm5_results
            
            self.db.add(assessment)
            self.db.commit()
            self.db.refresh(assessment)
            
            # Audit log
            self.audit_service.log_action("CREATE", "present_illness", assessment.id, new_values={
                "patient_id": assessment_data.patient_id,
                "primary_diagnosis": assessment_data.primary_diagnosis,
                "dsm5_criteria_met": dsm5_results is not None
            })
            
            logger.info(f"Assessment created successfully: ID {assessment.id}")
            return self._convert_to_data_model(assessment)
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to create assessment: {e}")
            raise
    
    def get_patient_assessments(self, patient_id: int) -> List[PresentIllnessData]:
        """Get all assessments for a patient"""
        logger.debug(f"Retrieving assessments for patient {patient_id}")
        
        # Verify access
        if not self._check_patient_access(patient_id):
            logger.warning(f"Access denied to patient {patient_id}")
            return []
        
        statement = select(PresentIllness).where(
            PresentIllness.patient_id == patient_id
        ).order_by(PresentIllness.assessment_date.desc())
        
        assessments = self.db.exec(statement).all()
        
        # Audit access
        self.audit_service.log_action("READ", "present_illness", None, new_values={
            "patient_id": patient_id,
            "assessments_count": len(assessments)
        })
        
        return [self._convert_to_data_model(a) for a in assessments]
    
    def evaluate_dsm5_criteria(self, disorder_code: str, responses: Dict[str, bool]) -> Dict[str, Any]:
        """Evaluate DSM-5-TR criteria for given responses"""
        try:
            result = self.dsm5_engine.evaluate_criteria(disorder_code, responses)
            
            # Audit DSM-5 evaluation
            self.audit_service.log_action("DSM5_EVAL", "dsm5_criteria", None, new_values={
                "disorder_code": disorder_code,
                "criteria_met": result.get('criteria_met', False),
                "confidence_level": result.get('confidence_level', 0)
            })
            
            return result
        except Exception as e:
            logger.error(f"DSM-5 evaluation failed: {e}")
            raise
    
    def _convert_to_data_model(self, assessment: PresentIllness) -> PresentIllnessData:
        """Convert database model to Pydantic data model"""
        return PresentIllnessData(
            id=assessment.id,
            patient_id=assessment.patient_id,
            assessment_date=assessment.assessment_date,
            chief_complaint=assessment.chief_complaint,
            history_present_illness=assessment.history_present_illness,
            primary_diagnosis=assessment.primary_diagnosis,
            secondary_diagnoses=assessment.secondary_diagnoses,
            dsm5_criteria_met=assessment.dsm5_criteria_met,
            treatment_plan=assessment.treatment_plan,
            created_at=assessment.created_at
        )
    
    def _check_patient_access(self, patient_id: int) -> bool:
        """Check if current user has access to patient"""
        from models.patient import UserPatientAccess
        statement = select(UserPatientAccess).where(
            UserPatientAccess.user_id == self.current_user_id,
            UserPatientAccess.patient_id == patient_id,
            UserPatientAccess.is_active == True
        )
        return self.db.exec(statement).first() is not None
```

## Service Implementation Notes

### Key Features
1. **Dependency Injection**: Services receive encryption interface, not singleton
2. **Comprehensive Validation**: Pydantic models ensure data integrity
3. **Access Control**: Every operation checks user permissions
4. **Audit Trail**: All operations logged with user attribution
5. **Error Handling**: Proper exception handling with secure error messages
6. **Patient Merging**: Complete data migration with tracking

### Usage Pattern
```python
# Example service usage in Reflex state
with get_db_session() as db:
    crypto_service = get_crypto_service()  # From DI container
    patient_service = PatientService(db, current_user_id, crypto_service)
    
    # Create patient with validation
    patient_data = PatientData(name="John Doe", dob="1990-01-01")
    new_patient = patient_service.create_patient(patient_data)
    
    # Search with pagination
    results = patient_service.search_patients("John", page=1, page_size=25)
```