# tests/integration/test_clinical_workflow.py
import pytest
from datetime import date
from models.patient import PatientData
from models.clinical import PresentIllnessData
from services.patient_service import PatientService
from services.clinical_service import ClinicalService

def test_clinical_assessment_creation(test_db, test_crypto_service):
    """Test clinical assessment creation with DSM-5-TR evaluation"""
    # First create a patient
    patient_service = PatientService(test_db, user_id=1, crypto_service=test_crypto_service)
    patient = patient_service.create_patient(PatientData(name="Test Patient", dob=date(1990, 1, 1)))
    
    # Create clinical assessment
    clinical_service = ClinicalService(test_db, user_id=1, crypto_service=test_crypto_service)
    
    assessment_data = PresentIllnessData(
        patient_id=patient.id,
        assessment_date=date.today(),
        chief_complaint="Feeling depressed",
        history_present_illness="Patient reports 2 weeks of depressed mood...",
        primary_diagnosis="296.22",
        treatment_plan="Start SSRI, therapy referral"
    )
    
    # DSM-5 responses
    dsm5_responses = {
        'mdd_1': True,
        'mdd_2': True,
        'mdd_3': True,
        'mdd_4': True,
        'mdd_5': True,
        'mdd_6': False,
        'mdd_7': False,
        'mdd_8': False,
        'mdd_9': False
    }
    
    created_assessment = clinical_service.create_assessment(assessment_data, dsm5_responses)
    
    assert created_assessment.id is not None
    assert created_assessment.patient_id == patient.id
    assert created_assessment.dsm5_criteria_met is not None
    assert created_assessment.dsm5_criteria_met['criteria_met'] == True

def test_complete_patient_clinical_workflow(test_db, test_crypto_service):
    """Test complete workflow from patient creation to clinical assessment"""
    # Step 1: Create patient
    patient_service = PatientService(test_db, user_id=1, crypto_service=test_crypto_service)
    patient_data = PatientData(
        name="Sarah Johnson",
        dob=date(1985, 3, 20),
        phone="**********",
        email="<EMAIL>"
    )
    patient = patient_service.create_patient(patient_data)
    
    # Step 2: Create initial assessment
    clinical_service = ClinicalService(test_db, user_id=1, crypto_service=test_crypto_service)
    
    initial_assessment = PresentIllnessData(
        patient_id=patient.id,
        assessment_date=date.today(),
        chief_complaint="Anxiety and panic attacks",
        history_present_illness="Patient reports sudden onset of panic attacks 3 months ago, with persistent worry about having more attacks.",
        primary_diagnosis="300.01",  # Panic Disorder
        treatment_plan="Cognitive behavioral therapy, consider SSRI if symptoms persist"
    )
    
    # DSM-5 responses for Panic Disorder
    panic_responses = {
        'pd_1': True,   # Recurrent panic attacks
        'pd_2': True,   # Concern about attacks
        'pd_3': True,   # Palpitations
        'pd_4': True,   # Sweating
        'pd_5': True,   # Trembling
        'pd_6': True,   # Shortness of breath
        'pd_7': False,  # Choking
        'pd_8': False,  # Chest pain
        'pd_9': False,  # Nausea
        'pd_10': False, # Dizziness
        'pd_11': False, # Chills
        'pd_12': False, # Paresthesias
        'pd_13': False, # Derealization
        'pd_14': True,  # Fear of losing control
        'pd_15': False  # Fear of dying
    }
    
    assessment1 = clinical_service.create_assessment(initial_assessment, panic_responses)
    
    # Step 3: Retrieve patient assessments
    assessments = clinical_service.get_patient_assessments(patient.id)
    assert len(assessments) == 1
    assert assessments[0].id == assessment1.id
    
    # Step 4: Create follow-up assessment
    followup_assessment = PresentIllnessData(
        patient_id=patient.id,
        assessment_date=date.today(),
        chief_complaint="Follow-up for panic disorder",
        history_present_illness="Patient reports improvement with CBT, panic attacks reduced from daily to weekly.",
        primary_diagnosis="300.01",
        treatment_plan="Continue CBT, add mindfulness techniques"
    )
    
    assessment2 = clinical_service.create_assessment(followup_assessment)
    
    # Step 5: Verify multiple assessments
    all_assessments = clinical_service.get_patient_assessments(patient.id)
    assert len(all_assessments) == 2
    
    # Should be ordered by date (most recent first)
    assert all_assessments[0].id == assessment2.id
    assert all_assessments[1].id == assessment1.id

def test_dsm5_criteria_evaluation_workflow(test_db, test_crypto_service):
    """Test DSM-5-TR criteria evaluation workflow"""
    clinical_service = ClinicalService(test_db, user_id=1, crypto_service=test_crypto_service)
    
    # Test Major Depressive Disorder evaluation
    mdd_responses = {
        'mdd_1': True,  # Depressed mood
        'mdd_2': True,  # Anhedonia
        'mdd_3': True,  # Weight changes
        'mdd_4': True,  # Sleep changes
        'mdd_5': True,  # Psychomotor changes
        'mdd_6': True,  # Fatigue
        'mdd_7': False, # Guilt
        'mdd_8': False, # Concentration
        'mdd_9': False  # Suicidal ideation
    }
    
    result = clinical_service.evaluate_dsm5_criteria('296.22', mdd_responses)
    
    assert result['disorder_code'] == '296.22'
    assert result['criteria_met'] == True  # 6 out of 9 criteria met, need 5
    assert result['met_criteria_count'] == 6
    assert result['required_criteria'] == 5
    assert result['confidence_level'] > 0.5

def test_clinical_access_control(test_db, test_crypto_service):
    """Test that clinical assessments respect patient access control"""
    # User 1 creates patient and assessment
    patient_service_user1 = PatientService(test_db, user_id=1, crypto_service=test_crypto_service)
    clinical_service_user1 = ClinicalService(test_db, user_id=1, crypto_service=test_crypto_service)
    
    patient = patient_service_user1.create_patient(PatientData(name="Private Patient", dob=date(1990, 1, 1)))
    
    assessment_data = PresentIllnessData(
        patient_id=patient.id,
        assessment_date=date.today(),
        chief_complaint="Test complaint",
        history_present_illness="Test history",
        primary_diagnosis="296.22"
    )
    
    assessment = clinical_service_user1.create_assessment(assessment_data)
    
    # User 2 should not be able to access the assessment
    clinical_service_user2 = ClinicalService(test_db, user_id=2, crypto_service=test_crypto_service)
    
    assessments_user2 = clinical_service_user2.get_patient_assessments(patient.id)
    assert len(assessments_user2) == 0  # Should not see any assessments

def test_clinical_data_validation(test_db, test_crypto_service):
    """Test clinical assessment data validation"""
    # Test invalid assessment data
    with pytest.raises(ValueError):
        PresentIllnessData(
            patient_id=0,  # Invalid patient ID
            assessment_date=date.today(),
            chief_complaint="",  # Empty complaint
            history_present_illness="Test history"
        )
    
    # Test valid assessment data
    valid_data = PresentIllnessData(
        patient_id=1,
        assessment_date=date.today(),
        chief_complaint="Valid complaint",
        history_present_illness="Valid history",
        primary_diagnosis="296.22"
    )
    
    assert valid_data.patient_id == 1
    assert valid_data.chief_complaint == "Valid complaint"

def test_multiple_diagnoses_workflow(test_db, test_crypto_service):
    """Test workflow with multiple diagnoses"""
    # Create patient
    patient_service = PatientService(test_db, user_id=1, crypto_service=test_crypto_service)
    patient = patient_service.create_patient(PatientData(name="Complex Patient", dob=date(1980, 1, 1)))
    
    # Create assessment with multiple diagnoses
    clinical_service = ClinicalService(test_db, user_id=1, crypto_service=test_crypto_service)
    
    assessment_data = PresentIllnessData(
        patient_id=patient.id,
        assessment_date=date.today(),
        chief_complaint="Depression and anxiety",
        history_present_illness="Patient presents with both depressive and anxiety symptoms...",
        primary_diagnosis="296.22",  # Major Depressive Disorder
        secondary_diagnoses="300.02",  # Generalized Anxiety Disorder
        treatment_plan="Dual approach: antidepressant and CBT for both conditions"
    )
    
    assessment = clinical_service.create_assessment(assessment_data)
    
    assert assessment.primary_diagnosis == "296.22"
    assert assessment.secondary_diagnoses == "300.02"
    assert "both conditions" in assessment.treatment_plan
