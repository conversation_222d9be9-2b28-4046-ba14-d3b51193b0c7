"""
Psychiatry EMR - Clinical Service
Clinical assessment business logic with DSM-5-TR integration.
"""

from typing import List, Optional, Dict, Any
from sqlmodel import Session, select
from datetime import datetime, date
import logging

from models.clinical import PresentIllness, PresentIllnessData
from models.patient import Patient, UserPatientAccess
from security.encryption import CryptoInterface
from services.audit_service import AuditService

logger = logging.getLogger(__name__)

class ClinicalService:
    """Clinical assessment business logic with DSM-5-TR integration"""
    
    def __init__(self, db_session: Session, current_user_id: int, crypto_service: CryptoInterface):
        self.db = db_session
        self.current_user_id = current_user_id
        self.crypto = crypto_service
        self.audit_service = AuditService(db_session, current_user_id)
        # DSM5 engine will be imported when needed to avoid circular imports
    
    def create_assessment(self, assessment_data: PresentIllnessData, dsm5_responses: Optional[Dict[str, bool]] = None) -> PresentIllnessData:
        """Create new clinical assessment with DSM-5-TR evaluation"""
        logger.info(f"Creating assessment for patient {assessment_data.patient_id}")
        
        # Verify patient access
        if not self._check_patient_access(assessment_data.patient_id):
            logger.error(f"Access denied to patient {assessment_data.patient_id}")
            raise PermissionError("Access denied to patient")
        
        try:
            # Evaluate DSM-5 criteria if responses provided
            dsm5_results = None
            if dsm5_responses and assessment_data.primary_diagnosis:
                # Import here to avoid circular imports
                from services.dsm5_engine import DSM5RuleEngine
                dsm5_engine = DSM5RuleEngine()
                dsm5_results = dsm5_engine.evaluate_criteria(
                    assessment_data.primary_diagnosis, 
                    dsm5_responses
                )
            
            # Create assessment record
            assessment = PresentIllness(
                patient_id=assessment_data.patient_id,
                assessment_date=assessment_data.assessment_date,
                chief_complaint=assessment_data.chief_complaint,
                history_present_illness=assessment_data.history_present_illness,
                primary_diagnosis=assessment_data.primary_diagnosis,
                secondary_diagnoses=assessment_data.secondary_diagnoses,
                treatment_plan=assessment_data.treatment_plan,
                updated_by=self.current_user_id
            )
            
            # Store DSM-5 results
            if dsm5_results:
                assessment.dsm5_criteria_met = dsm5_results
            
            self.db.add(assessment)
            self.db.commit()
            self.db.refresh(assessment)
            
            # Audit log
            self.audit_service.log_action("CREATE", "present_illness", assessment.id, new_values={
                "patient_id": assessment_data.patient_id,
                "primary_diagnosis": assessment_data.primary_diagnosis,
                "dsm5_criteria_met": dsm5_results is not None
            })
            
            logger.info(f"Assessment created successfully: ID {assessment.id}")
            return self._convert_to_data_model(assessment)
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to create assessment: {e}")
            raise
    
    def get_patient_assessments(self, patient_id: int) -> List[PresentIllnessData]:
        """Get all assessments for a patient"""
        logger.debug(f"Retrieving assessments for patient {patient_id}")
        
        # Verify access
        if not self._check_patient_access(patient_id):
            logger.warning(f"Access denied to patient {patient_id}")
            return []
        
        statement = select(PresentIllness).where(
            PresentIllness.patient_id == patient_id
        ).order_by(PresentIllness.assessment_date.desc())
        
        assessments = self.db.exec(statement).all()
        
        # Audit access
        self.audit_service.log_action("READ", "present_illness", None, new_values={
            "patient_id": patient_id,
            "assessments_count": len(assessments)
        })
        
        return [self._convert_to_data_model(a) for a in assessments]

    def get_assessment(self, assessment_id: int) -> Optional[PresentIllnessData]:
        """Get a specific assessment by ID"""
        logger.debug(f"Retrieving assessment {assessment_id}")

        assessment = self.db.get(PresentIllness, assessment_id)
        if not assessment:
            return None

        # Verify patient access
        if not self._check_patient_access(assessment.patient_id):
            logger.warning(f"Access denied to assessment {assessment_id}")
            return None

        # Audit access
        self.audit_service.log_action("READ", "present_illness", assessment_id)

        return self._convert_to_data_model(assessment)

    def update_assessment(self, assessment_id: int, assessment_data: PresentIllnessData, dsm5_responses: Optional[Dict[str, bool]] = None) -> Optional[PresentIllnessData]:
        """Update an existing assessment"""
        logger.info(f"Updating assessment {assessment_id}")

        assessment = self.db.get(PresentIllness, assessment_id)
        if not assessment:
            return None

        # Verify patient access
        if not self._check_patient_access(assessment.patient_id):
            logger.error(f"Access denied to assessment {assessment_id}")
            raise PermissionError("Access denied to assessment")

        try:
            # Store old values for audit
            old_values = {
                "chief_complaint": assessment.chief_complaint,
                "primary_diagnosis": assessment.primary_diagnosis,
                "treatment_plan": assessment.treatment_plan
            }

            # Update assessment fields
            assessment.assessment_date = assessment_data.assessment_date
            assessment.chief_complaint = assessment_data.chief_complaint
            assessment.history_present_illness = assessment_data.history_present_illness
            assessment.primary_diagnosis = assessment_data.primary_diagnosis
            assessment.secondary_diagnoses = assessment_data.secondary_diagnoses
            assessment.treatment_plan = assessment_data.treatment_plan
            assessment.updated_by = self.current_user_id

            # Re-evaluate DSM-5 criteria if responses provided
            if dsm5_responses and assessment_data.primary_diagnosis:
                from services.dsm5_engine import DSM5RuleEngine
                dsm5_engine = DSM5RuleEngine()
                dsm5_results = dsm5_engine.evaluate_criteria(
                    assessment_data.primary_diagnosis,
                    dsm5_responses
                )
                assessment.dsm5_criteria_met = dsm5_results

            self.db.commit()
            self.db.refresh(assessment)

            # Audit log
            self.audit_service.log_action("UPDATE", "present_illness", assessment_id,
                old_values=old_values,
                new_values={
                    "chief_complaint": assessment_data.chief_complaint,
                    "primary_diagnosis": assessment_data.primary_diagnosis,
                    "treatment_plan": assessment_data.treatment_plan
                }
            )

            logger.info(f"Assessment updated successfully: ID {assessment_id}")
            return self._convert_to_data_model(assessment)

        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to update assessment: {e}")
            raise

    def evaluate_dsm5_criteria(self, disorder_code: str, responses: Dict[str, bool]) -> Dict[str, Any]:
        """Evaluate DSM-5-TR criteria for given responses"""
        try:
            # Import here to avoid circular imports
            from services.dsm5_engine import DSM5RuleEngine
            dsm5_engine = DSM5RuleEngine()
            result = dsm5_engine.evaluate_criteria(disorder_code, responses)
            
            # Audit DSM-5 evaluation
            self.audit_service.log_action("DSM5_EVAL", "dsm5_criteria", None, new_values={
                "disorder_code": disorder_code,
                "criteria_met": result.get('criteria_met', False),
                "confidence_level": result.get('confidence_level', 0)
            })
            
            return result
        except Exception as e:
            logger.error(f"DSM-5 evaluation failed: {e}")
            raise
    
    def _convert_to_data_model(self, assessment: PresentIllness) -> PresentIllnessData:
        """Convert database model to Pydantic data model"""
        return PresentIllnessData(
            id=assessment.id,
            patient_id=assessment.patient_id,
            assessment_date=assessment.assessment_date,
            chief_complaint=assessment.chief_complaint,
            history_present_illness=assessment.history_present_illness,
            primary_diagnosis=assessment.primary_diagnosis,
            secondary_diagnoses=assessment.secondary_diagnoses,
            dsm5_criteria_met=assessment.dsm5_criteria_met,
            treatment_plan=assessment.treatment_plan,
            created_at=assessment.created_at
        )
    
    def _check_patient_access(self, patient_id: int) -> bool:
        """Check if current user has access to patient"""
        statement = select(UserPatientAccess).where(
            UserPatientAccess.user_id == self.current_user_id,
            UserPatientAccess.patient_id == patient_id,
            UserPatientAccess.is_active == True
        )
        return self.db.exec(statement).first() is not None
