-- Psychiatry EMR - Database Security Setup (SQLite Compatible)
-- Basic security setup for SQLite database
-- Note: SQLite has limited security features compared to PostgreSQL

-- Create basic audit trigger (simplified for SQLite)
-- SQLite doesn't support triggers with complex logic like PostgreSQL
-- We'll use application-level audit logging instead

-- Create indexes for audit log performance
CREATE INDEX IF NOT EXISTS idx_audit_log_user_timestamp ON audit_log(user_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_audit_log_table_record ON audit_log(table_name, record_id);
CREATE INDEX IF NOT EXISTS idx_audit_log_action ON audit_log(action, timestamp DESC);

-- Create basic user access indexes
CREATE INDEX IF NOT EXISTS idx_user_patient_access_user ON user_patient_access(user_id);
CREATE INDEX IF NOT EXISTS idx_user_patient_access_patient ON user_patient_access(patient_id);
CREATE INDEX IF NOT EXISTS idx_user_patient_access_active ON user_patient_access(is_active);

-- Create patient access indexes
CREATE INDEX IF NOT EXISTS idx_patient_active ON patient(is_active);

-- Note: SQLite limitations:
-- 1. No CREATE EXTENSION support
-- 2. No advanced security features like RLS (Row Level Security)
-- 3. No pgcrypto for encryption
-- 4. No PL/pgSQL functions
-- 5. No advanced user management

-- These security features will need to be implemented at the application level:
-- - User authentication and authorization
-- - Row-level access control
-- - Audit logging
-- - Data encryption
-- - Password hashing

-- Basic table permissions (SQLite doesn't have GRANT/REVOKE)
-- Access control must be handled by the application

-- Update statistics
ANALYZE;