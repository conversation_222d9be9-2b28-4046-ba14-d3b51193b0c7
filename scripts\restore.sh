#!/bin/bash
# restore.sh - Comprehensive Recovery Script for Psychiatry EMR
# HIPAA-compliant encrypted backup restoration with verification

set -euo pipefail

# Make script executable
chmod +x "$0" 2>/dev/null || true

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BACKUP_DIR="${BACKUP_DIR:-/var/lib/psychiatry-emr/backups}"
RESTORE_DIR="${RESTORE_DIR:-/tmp/psychiatry-emr-restore}"
ENCRYPTION_KEY_FILE="${ENCRYPTION_KEY_FILE:-/etc/psychiatry-emr/backup.key}"
LOG_FILE="${LOG_FILE:-/var/log/psychiatry-emr/restore.log}"

# Database configuration
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"
DB_NAME="${POSTGRES_DB:-psychiatry_emr}"
DB_USER="${POSTGRES_USER:-postgres}"
DB_PASSWORD="${POSTGRES_PASSWORD}"

# Logging functions
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${timestamp} [${level}] ${message}" | tee -a "$LOG_FILE"
}

log_info() {
    log "INFO" "$@"
    echo -e "${BLUE}[INFO]${NC} $*"
}

log_success() {
    log "SUCCESS" "$@"
    echo -e "${GREEN}[SUCCESS]${NC} $*"
}

log_warning() {
    log "WARNING" "$@"
    echo -e "${YELLOW}[WARNING]${NC} $*"
}

log_error() {
    log "ERROR" "$@"
    echo -e "${RED}[ERROR]${NC} $*"
}

# Check prerequisites for restoration
check_restore_prerequisites() {
    log_info "Checking restore prerequisites..."
    
    # Check required commands
    local required_commands=("pg_restore" "psql" "gpg" "tar" "gzip" "jq")
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            log_error "Required command not found: $cmd"
            exit 1
        fi
    done
    
    # Check backup directory
    if [ ! -d "$BACKUP_DIR" ]; then
        log_error "Backup directory not found: $BACKUP_DIR"
        exit 1
    fi
    
    # Check encryption key
    if [ ! -f "$ENCRYPTION_KEY_FILE" ]; then
        log_error "Encryption key file not found: $ENCRYPTION_KEY_FILE"
        exit 1
    fi
    
    # Create restore directory
    if [ ! -d "$RESTORE_DIR" ]; then
        mkdir -p "$RESTORE_DIR"
    fi
    
    log_success "Prerequisites check completed"
}

# List available backups
list_backups() {
    log_info "Available backups in $BACKUP_DIR:"
    echo
    
    # Find manifest files to get backup sets
    local manifests=($(find "$BACKUP_DIR" -name "manifest_*.json.gpg" -type f | sort -r))
    
    if [ ${#manifests[@]} -eq 0 ]; then
        log_warning "No backup manifests found"
        return 1
    fi
    
    echo "Timestamp        Date                 Database    Application  Configuration"
    echo "---------------- -------------------- ----------- ------------ -------------"
    
    for manifest in "${manifests[@]}"; do
        local timestamp=$(basename "$manifest" | sed 's/manifest_\(.*\)\.json\.gpg/\1/')
        
        # Decrypt and parse manifest
        local temp_manifest="/tmp/manifest_${timestamp}.json"
        if gpg --quiet --batch --passphrase-file "$ENCRYPTION_KEY_FILE" --decrypt "$manifest" > "$temp_manifest" 2>/dev/null; then
            local backup_date=$(jq -r '.backup_date' "$temp_manifest" 2>/dev/null || echo "Unknown")
            local db_size=$(jq -r '.database_backup.size' "$temp_manifest" 2>/dev/null || echo "0")
            local app_size=$(jq -r '.application_backup.size' "$temp_manifest" 2>/dev/null || echo "0")
            local config_size=$(jq -r '.configuration_backup.size' "$temp_manifest" 2>/dev/null || echo "0")
            
            # Format sizes
            db_size=$(numfmt --to=iec --suffix=B "$db_size" 2>/dev/null || echo "${db_size}B")
            app_size=$(numfmt --to=iec --suffix=B "$app_size" 2>/dev/null || echo "${app_size}B")
            config_size=$(numfmt --to=iec --suffix=B "$config_size" 2>/dev/null || echo "${config_size}B")
            
            printf "%-16s %-20s %-11s %-12s %-13s\n" \
                "$timestamp" "$backup_date" "$db_size" "$app_size" "$config_size"
            
            rm -f "$temp_manifest"
        else
            printf "%-16s %-20s %-11s %-12s %-13s\n" \
                "$timestamp" "Decrypt failed" "Unknown" "Unknown" "Unknown"
        fi
    done
    echo
}

# Decrypt backup file
decrypt_backup() {
    local encrypted_file="$1"
    local output_file="$2"
    
    log_info "Decrypting backup: $(basename "$encrypted_file")"
    
    if gpg --quiet \
           --batch \
           --passphrase-file "$ENCRYPTION_KEY_FILE" \
           --decrypt "$encrypted_file" > "$output_file" 2>/dev/null; then
        log_success "Backup decrypted: $(basename "$output_file")"
        return 0
    else
        log_error "Failed to decrypt backup: $(basename "$encrypted_file")"
        return 1
    fi
}

# Restore database
restore_database() {
    local timestamp="$1"
    local force="${2:-false}"
    
    log_info "Restoring database from backup: $timestamp"
    
    # Find database backup file
    local db_backup_file="$BACKUP_DIR/database_${timestamp}.sql.gz.gpg"
    if [ ! -f "$db_backup_file" ]; then
        log_error "Database backup file not found: $db_backup_file"
        return 1
    fi
    
    # Decrypt and decompress database backup
    local temp_db_file="$RESTORE_DIR/database_${timestamp}.sql"
    if ! decrypt_backup "$db_backup_file" "$temp_db_file.gz"; then
        return 1
    fi
    
    log_info "Decompressing database backup..."
    gunzip "$temp_db_file.gz"
    
    # Check database connectivity
    if ! PGPASSWORD="$DB_PASSWORD" pg_isready -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" &> /dev/null; then
        log_error "Cannot connect to database server"
        return 1
    fi
    
    # Backup current database if it exists (unless force restore)
    if [ "$force" != "true" ]; then
        log_info "Creating backup of current database before restore..."
        local current_backup="$RESTORE_DIR/current_db_backup_$(date +%Y%m%d_%H%M%S).sql"
        PGPASSWORD="$DB_PASSWORD" pg_dump \
            -h "$DB_HOST" \
            -p "$DB_PORT" \
            -U "$DB_USER" \
            -d "$DB_NAME" \
            --file="$current_backup" \
            2>> "$LOG_FILE" || log_warning "Failed to backup current database"
    fi
    
    # Drop and recreate database
    log_warning "Dropping existing database: $DB_NAME"
    PGPASSWORD="$DB_PASSWORD" psql \
        -h "$DB_HOST" \
        -p "$DB_PORT" \
        -U "$DB_USER" \
        -d postgres \
        -c "DROP DATABASE IF EXISTS $DB_NAME;" \
        2>> "$LOG_FILE"
    
    log_info "Creating new database: $DB_NAME"
    PGPASSWORD="$DB_PASSWORD" psql \
        -h "$DB_HOST" \
        -p "$DB_PORT" \
        -U "$DB_USER" \
        -d postgres \
        -c "CREATE DATABASE $DB_NAME;" \
        2>> "$LOG_FILE"
    
    # Restore database
    log_info "Restoring database from backup..."
    PGPASSWORD="$DB_PASSWORD" pg_restore \
        -h "$DB_HOST" \
        -p "$DB_PORT" \
        -U "$DB_USER" \
        -d "$DB_NAME" \
        --verbose \
        --no-password \
        "$temp_db_file" \
        2>> "$LOG_FILE"
    
    if [ $? -eq 0 ]; then
        log_success "Database restored successfully"
        rm -f "$temp_db_file"
        return 0
    else
        log_error "Database restore failed"
        return 1
    fi
}

# Restore application files
restore_application_files() {
    local timestamp="$1"
    local target_dir="${2:-$PROJECT_ROOT}"
    
    log_info "Restoring application files from backup: $timestamp"
    
    # Find application backup file
    local app_backup_file="$BACKUP_DIR/application_${timestamp}.tar.gz.gpg"
    if [ ! -f "$app_backup_file" ]; then
        log_error "Application backup file not found: $app_backup_file"
        return 1
    fi
    
    # Decrypt application backup
    local temp_app_file="$RESTORE_DIR/application_${timestamp}.tar.gz"
    if ! decrypt_backup "$app_backup_file" "$temp_app_file"; then
        return 1
    fi
    
    # Extract application files
    log_info "Extracting application files to: $target_dir"
    cd "$target_dir"
    tar -xzf "$temp_app_file" 2>> "$LOG_FILE"
    
    if [ $? -eq 0 ]; then
        log_success "Application files restored successfully"
        rm -f "$temp_app_file"
        return 0
    else
        log_error "Application files restore failed"
        return 1
    fi
}

# Restore configuration files
restore_configuration() {
    local timestamp="$1"
    local target_dir="${2:-$PROJECT_ROOT}"
    
    log_info "Restoring configuration from backup: $timestamp"
    
    # Find configuration backup file
    local config_backup_file="$BACKUP_DIR/configuration_${timestamp}.tar.gz.gpg"
    if [ ! -f "$config_backup_file" ]; then
        log_error "Configuration backup file not found: $config_backup_file"
        return 1
    fi
    
    # Decrypt configuration backup
    local temp_config_file="$RESTORE_DIR/configuration_${timestamp}.tar.gz"
    if ! decrypt_backup "$config_backup_file" "$temp_config_file"; then
        return 1
    fi
    
    # Extract configuration files
    log_info "Extracting configuration files to: $target_dir"
    cd "$target_dir"
    tar -xzf "$temp_config_file" 2>> "$LOG_FILE"
    
    if [ $? -eq 0 ]; then
        log_success "Configuration restored successfully"
        rm -f "$temp_config_file"
        return 0
    else
        log_error "Configuration restore failed"
        return 1
    fi
}

# Verify restored system
verify_restore() {
    log_info "Verifying restored system..."
    
    # Check database connectivity and basic structure
    if PGPASSWORD="$DB_PASSWORD" psql \
        -h "$DB_HOST" \
        -p "$DB_PORT" \
        -U "$DB_USER" \
        -d "$DB_NAME" \
        -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" \
        &> /dev/null; then
        log_success "Database verification passed"
    else
        log_error "Database verification failed"
        return 1
    fi
    
    # Check critical application files
    local critical_files=("main.py" "pyproject.toml" "alembic.ini")
    for file in "${critical_files[@]}"; do
        if [ -f "$PROJECT_ROOT/$file" ]; then
            log_success "Critical file found: $file"
        else
            log_error "Critical file missing: $file"
            return 1
        fi
    done
    
    log_success "System verification completed"
    return 0
}

# Show usage information
show_usage() {
    echo "Usage: $0 [OPTIONS] COMMAND [ARGS]"
    echo
    echo "Commands:"
    echo "  list                     List available backups"
    echo "  restore <timestamp>      Restore complete system from backup"
    echo "  restore-db <timestamp>   Restore only database"
    echo "  restore-app <timestamp>  Restore only application files"
    echo "  restore-config <timestamp> Restore only configuration"
    echo "  verify                   Verify current system"
    echo
    echo "Options:"
    echo "  --force                  Force restore without current backup"
    echo "  --target-dir <dir>       Target directory for file restoration"
    echo "  --help                   Show this help message"
    echo
    echo "Examples:"
    echo "  $0 list"
    echo "  $0 restore 20240115_143022"
    echo "  $0 restore-db 20240115_143022 --force"
    echo "  $0 restore-app 20240115_143022 --target-dir /opt/psychiatry-emr"
}

# Main function
main() {
    local command="${1:-}"
    local timestamp="${2:-}"
    local force="false"
    local target_dir="$PROJECT_ROOT"
    
    # Parse options
    while [[ $# -gt 0 ]]; do
        case $1 in
            --force)
                force="true"
                shift
                ;;
            --target-dir)
                target_dir="$2"
                shift 2
                ;;
            --help)
                show_usage
                exit 0
                ;;
            *)
                break
                ;;
        esac
    done
    
    # Update command and timestamp after option parsing
    command="${1:-}"
    timestamp="${2:-}"
    
    case "$command" in
        list)
            check_restore_prerequisites
            list_backups
            ;;
        restore)
            if [ -z "$timestamp" ]; then
                log_error "Please specify backup timestamp"
                show_usage
                exit 1
            fi
            
            log_info "Starting complete system restore: $timestamp"
            check_restore_prerequisites
            
            if restore_database "$timestamp" "$force" && \
               restore_application_files "$timestamp" "$target_dir" && \
               restore_configuration "$timestamp" "$target_dir" && \
               verify_restore; then
                log_success "Complete system restore completed successfully"
            else
                log_error "System restore failed"
                exit 1
            fi
            ;;
        restore-db)
            if [ -z "$timestamp" ]; then
                log_error "Please specify backup timestamp"
                exit 1
            fi
            
            check_restore_prerequisites
            restore_database "$timestamp" "$force"
            ;;
        restore-app)
            if [ -z "$timestamp" ]; then
                log_error "Please specify backup timestamp"
                exit 1
            fi
            
            check_restore_prerequisites
            restore_application_files "$timestamp" "$target_dir"
            ;;
        restore-config)
            if [ -z "$timestamp" ]; then
                log_error "Please specify backup timestamp"
                exit 1
            fi
            
            check_restore_prerequisites
            restore_configuration "$timestamp" "$target_dir"
            ;;
        verify)
            verify_restore
            ;;
        "")
            log_error "No command specified"
            show_usage
            exit 1
            ;;
        *)
            log_error "Unknown command: $command"
            show_usage
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
