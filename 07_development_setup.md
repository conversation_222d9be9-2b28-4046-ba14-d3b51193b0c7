# Development Environment Setup

## Requirements Files Structure

### Production Dependencies
```python
# requirements.txt - Production dependencies
reflex>=0.4.0
sqlmodel
psycopg2-binary
pandas
python-dotenv
pydantic>=2.0
cryptography
pyyaml
fuzzywuzzy
alembic  # Database migrations
```

### Development Dependencies
```python
# requirements-dev.txt - Development dependencies
pytest
pytest-postgresql
pytest-benchmark
pytest-cov
pytest-html
playwright
black
ruff
pre-commit
mypy
```

## Pre-commit Configuration

```yaml
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/psf/black
    rev: 23.3.0
    hooks:
      - id: black
        language_version: python3.11
  
  - repo: https://github.com/charliermarsh/ruff-pre-commit
    rev: v0.0.270
    hooks:
      - id: ruff
        args: [--fix, --exit-non-zero-on-fix]
  
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.3.0
    hooks:
      - id: mypy
        additional_dependencies: [pydantic, sqlmodel]
  
  - repo: local
    hooks:
      - id: pytest-quick
        name: pytest-quick
        entry: pytest tests/unit/ -x --tb=short
        language: system
        pass_filenames: false
        always_run: true
```

## Enhanced Development Setup Script

```bash
#!/bin/bash
# setup_dev.sh - Enhanced development environment setup

set -e  # Exit on any error

echo "🏥 Setting up Psychiatry EMR Development Environment"

# Check prerequisites
command -v python3 >/dev/null 2>&1 || { echo "❌ Python 3 is required but not installed."; exit 1; }
command -v psql >/dev/null 2>&1 || { echo "❌ PostgreSQL client is required but not installed."; exit 1; }

# Create virtual environment
echo "📦 Creating virtual environment..."
python3 -m venv venv
source venv/bin/activate

# Install dependencies
echo "📚 Installing dependencies..."
pip install --upgrade pip
pip install -r requirements.txt
pip install -r requirements-dev.txt

# Setup pre-commit hooks
echo "🔧 Setting up pre-commit hooks..."
pre-commit install

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "⚙️ Creating .env file..."
    python3 -c "
from config.settings import generate_env_template
with open('.env', 'w') as f:
    f.write(generate_env_template())
print('✅ .env file created with secure defaults')
"
fi

# Database setup
DB_NAME=${DB_NAME:-psychiatry_emr_dev}
echo "🗄️ Setting up database: $DB_NAME"

# Check if database exists
if psql -lqt | cut -d \| -f 1 | grep -qw $DB_NAME; then
    echo "ℹ️ Database $DB_NAME already exists"
else
    createdb $DB_NAME
    echo "✅ Database $DB_NAME created"
fi

# Enable extensions
psql -d $DB_NAME -c "CREATE EXTENSION IF NOT EXISTS pgcrypto;" -q
psql -d $DB_NAME -c "CREATE EXTENSION IF NOT EXISTS pg_trgm;" -q  # For text search
echo "✅ Database extensions enabled"

# Initialize Alembic for migrations
if [ ! -d "alembic" ]; then
    echo "🔄 Initializing database migrations..."
    alembic init alembic
    echo "✅ Alembic initialized"
fi

# Run database migrations
echo "🔄 Running database migrations..."
reflex db migrate

# Apply custom indexes
echo "📊 Creating performance indexes..."
psql -d $DB_NAME -f sql/performance_indexes.sql -q

echo "✅ Development environment setup complete!"
echo ""
echo "Next steps:"
echo "1. source venv/bin/activate"
echo "2. Enter master password when prompted: reflex run"
echo "3. Open http://localhost:3000"
echo ""
echo "🧪 Run tests with: pytest"
echo "🎨 Format code with: black ."
echo "🔍 Lint code with: ruff check ."
```

## Development Workflow Scripts

### Code Quality Script
```bash
#!/bin/bash
# scripts/check_code_quality.sh - Code quality verification

echo "🔍 Running code quality checks..."

# Format code
echo "🎨 Formatting code with Black..."
black .

# Lint code
echo "🔍 Linting with Ruff..."
ruff check . --fix

# Type checking
echo "🏷️ Type checking with MyPy..."
mypy .

# Security check
echo "🔒 Security check with Bandit..."
bandit -r . -f json -o reports/security_report.json

# Import sorting
echo "📦 Sorting imports..."
isort .

echo "✅ Code quality checks complete!"
```

### Testing Script
```bash
#!/bin/bash
# scripts/run_tests.sh - Comprehensive testing script

echo "🧪 Running comprehensive test suite..."

# Create reports directory
mkdir -p reports

# Unit tests with coverage
echo "🔬 Running unit tests..."
pytest tests/unit/ --cov=. --cov-report=html:reports/coverage --cov-report=xml -v

# Security tests
echo "🔒 Running security tests..."
pytest tests/security/ -v --tb=short

# Integration tests
echo "🔗 Running integration tests..."
pytest tests/integration/ -v

# Performance benchmarks
echo "⚡ Running performance benchmarks..."
pytest tests/performance/ -v --benchmark-only --benchmark-json=reports/benchmark.json

# Generate HTML test report
echo "📊 Generating test report..."
pytest --html=reports/test_report.html --self-contained-html

echo "✅ All tests completed! Check reports/ directory for detailed results."
```

### Database Management Scripts

```bash
#!/bin/bash
# scripts/manage_db.sh - Database management utilities

function create_migration() {
    echo "📝 Creating new migration..."
    alembic revision --autogenerate -m "$1"
}

function apply_migrations() {
    echo "🔄 Applying database migrations..."
    alembic upgrade head
}

function rollback_migration() {
    echo "⏪ Rolling back last migration..."
    alembic downgrade -1
}

function reset_database() {
    echo "🗑️ Resetting database..."
    read -p "Are you sure? This will delete all data. (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        dropdb psychiatry_emr_dev
        createdb psychiatry_emr_dev
        psql -d psychiatry_emr_dev -c "CREATE EXTENSION IF NOT EXISTS pgcrypto;"
        psql -d psychiatry_emr_dev -c "CREATE EXTENSION IF NOT EXISTS pg_trgm;"
        reflex db migrate
        echo "✅ Database reset complete"
    fi
}

function backup_database() {
    echo "💾 Creating database backup..."
    timestamp=$(date +%Y%m%d_%H%M%S)
    pg_dump psychiatry_emr_dev > "backups/dev_backup_$timestamp.sql"
    echo "✅ Backup created: backups/dev_backup_$timestamp.sql"
}

# Command dispatcher
case "$1" in
    "migrate")
        create_migration "$2"
        ;;
    "upgrade")
        apply_migrations
        ;;
    "downgrade")
        rollback_migration
        ;;
    "reset")
        reset_database
        ;;
    "backup")
        backup_database
        ;;
    *)
        echo "Usage: $0 {migrate|upgrade|downgrade|reset|backup} [message]"
        echo ""
        echo "Commands:"
        echo "  migrate <message>  - Create new migration"
        echo "  upgrade           - Apply pending migrations"
        echo "  downgrade         - Rollback last migration"
        echo "  reset             - Reset database (WARNING: destroys data)"
        echo "  backup            - Create database backup"
        exit 1
        ;;
esac
```

## Development Configuration Files

### VS Code Settings
```json
{
  ".vscode/settings.json": {
    "python.defaultInterpreterPath": "./venv/bin/python",
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": false,
    "python.linting.flake8Enabled": false,
    "python.linting.mypyEnabled": true,
    "python.formatting.provider": "black",
    "python.sortImports.args": ["--profile", "black"],
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
      "source.organizeImports": true
    },
    "files.exclude": {
      "**/__pycache__": true,
      "**/*.pyc": true,
      ".mypy_cache": true,
      ".pytest_cache": true,
      "*.egg-info": true
    }
  }
}
```

### PyTest Configuration
```ini
# pytest.ini
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_functions = test_*
python_classes = Test*
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    security: marks tests as security-related
    integration: marks tests as integration tests
    unit: marks tests as unit tests
```

### Black Configuration
```toml
# pyproject.toml
[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.ruff]
line-length = 88
target-version = "py311"
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "S",  # flake8-bandit (security)
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
    "N",  # pep8-naming
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "S101",  # use of assert detected (common in tests)
    "S603",  # subprocess call: check for execution of untrusted input
    "S607",  # starting a process with a partial executable path
]

[tool.ruff.per-file-ignores]
"tests/*" = ["S101", "S106"]  # Allow asserts and hardcoded passwords in tests
"scripts/*" = ["S602", "S603"]  # Allow subprocess calls in scripts

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = [
    "models",
    "services", 
    "security",
    "config",
    "states",
    "pages",
    "components"
]