# tests/integration/test_dsm5_engine.py
import pytest
from services.dsm5_engine import DSM5RuleEngine, CriterionRule, DisorderCriteria

def test_dsm5_criteria_validation():
    """Test DSM-5-TR criteria validation"""
    # Test valid criterion
    criterion = CriterionRule(id="test_1", description="Test criterion", required=True)
    assert criterion.id == "test_1"
    
    # Test invalid criterion ID
    with pytest.raises(ValueError):
        CriterionRule(id="", description="Test", required=True)

def test_dsm5_evaluation():
    """Test diagnostic criteria evaluation"""
    engine = DSM5RuleEngine()
    
    # Test MDD evaluation
    responses = {
        'mdd_1': True,  # Depressed mood
        'mdd_2': True,  # Anhedonia
        'mdd_3': True,  # Weight changes
        'mdd_4': True,  # Sleep changes
        'mdd_5': True,  # Psychomotor changes
        'mdd_6': False, # Fatigue
        'mdd_7': False, # Guilt
        'mdd_8': False, # Concentration
        'mdd_9': False  # Suicidal ideation
    }
    
    result = engine.evaluate_criteria('296.22', responses)
    assert result['criteria_met'] == True  # 5 out of 9 criteria met
    assert result['met_criteria_count'] == 5
    assert result['disorder_code'] == '296.22'

def test_dsm5_gad_evaluation():
    """Test Generalized Anxiety Disorder evaluation"""
    engine = DSM5RuleEngine()
    
    # Test GAD evaluation - need 3 criteria plus the required ones
    responses = {
        'gad_1': True,  # Excessive anxiety (required)
        'gad_2': True,  # Difficulty controlling worry (required)
        'gad_3': True,  # Restlessness
        'gad_4': True,  # Fatigue
        'gad_5': True,  # Concentration problems
        'gad_6': False, # Irritability
        'gad_7': False, # Muscle tension
        'gad_8': False  # Sleep disturbance
    }
    
    result = engine.evaluate_criteria('300.02', responses)
    assert result['criteria_met'] == True  # 5 criteria met, need 3
    assert result['met_criteria_count'] == 5
    assert result['disorder_code'] == '300.02'
    assert result['disorder_name'] == 'Generalized Anxiety Disorder'

def test_dsm5_insufficient_criteria():
    """Test evaluation when insufficient criteria are met"""
    engine = DSM5RuleEngine()
    
    # Test MDD with insufficient criteria
    responses = {
        'mdd_1': True,  # Depressed mood
        'mdd_2': False, # Anhedonia
        'mdd_3': True,  # Weight changes
        'mdd_4': False, # Sleep changes
        'mdd_5': False, # Psychomotor changes
        'mdd_6': False, # Fatigue
        'mdd_7': False, # Guilt
        'mdd_8': False, # Concentration
        'mdd_9': False  # Suicidal ideation
    }
    
    result = engine.evaluate_criteria('296.22', responses)
    assert result['criteria_met'] == False  # Only 2 criteria met, need 5
    assert result['met_criteria_count'] == 2
    assert result['required_criteria'] == 5

def test_dsm5_unknown_disorder():
    """Test evaluation with unknown disorder code"""
    engine = DSM5RuleEngine()
    
    with pytest.raises(ValueError, match="Unknown disorder code"):
        engine.evaluate_criteria('999.99', {})

def test_dsm5_list_disorders():
    """Test listing available disorders"""
    engine = DSM5RuleEngine()
    
    disorders = engine.list_available_disorders()
    assert len(disorders) > 0
    
    # Check that MDD and GAD are in the list
    disorder_codes = [d['code'] for d in disorders]
    assert '296.22' in disorder_codes
    assert '300.02' in disorder_codes

def test_dsm5_get_disorder_criteria():
    """Test getting specific disorder criteria"""
    engine = DSM5RuleEngine()
    
    # Test getting MDD criteria
    mdd_criteria = engine.get_disorder_criteria('296.22')
    assert mdd_criteria is not None
    assert mdd_criteria.disorder_code == '296.22'
    assert mdd_criteria.minimum_criteria == 5
    assert len(mdd_criteria.criteria) == 9
    
    # Test getting non-existent disorder
    unknown_criteria = engine.get_disorder_criteria('999.99')
    assert unknown_criteria is None

def test_dsm5_confidence_calculation():
    """Test confidence level calculation"""
    engine = DSM5RuleEngine()
    
    # Test high confidence (all criteria met)
    all_responses = {f'mdd_{i}': True for i in range(1, 10)}
    result = engine.evaluate_criteria('296.22', all_responses)
    assert result['confidence_level'] == 1.0
    
    # Test medium confidence (minimum criteria met)
    min_responses = {
        'mdd_1': True,
        'mdd_2': True,
        'mdd_3': True,
        'mdd_4': True,
        'mdd_5': True,
        'mdd_6': False,
        'mdd_7': False,
        'mdd_8': False,
        'mdd_9': False
    }
    result = engine.evaluate_criteria('296.22', min_responses)
    assert result['confidence_level'] == 5/9  # 5 out of 9 criteria
    
    # Test low confidence (insufficient criteria)
    low_responses = {
        'mdd_1': True,
        'mdd_2': True,
        'mdd_3': False,
        'mdd_4': False,
        'mdd_5': False,
        'mdd_6': False,
        'mdd_7': False,
        'mdd_8': False,
        'mdd_9': False
    }
    result = engine.evaluate_criteria('296.22', low_responses)
    assert result['confidence_level'] == 2/9  # 2 out of 9 criteria

def test_dsm5_ptsd_evaluation():
    """Test PTSD evaluation"""
    engine = DSM5RuleEngine()
    
    # Test PTSD evaluation
    responses = {
        'ptsd_1': True,  # Trauma exposure (required)
        'ptsd_2': True,  # Intrusive memories
        'ptsd_3': True,  # Distressing dreams
        'ptsd_4': False, # Flashbacks
        'ptsd_5': False, # Psychological distress
        'ptsd_6': False, # Physiological reactions
        'ptsd_7': True,  # Avoidance
        'ptsd_8': True,  # Negative cognitions
        'ptsd_9': True   # Arousal changes
    }
    
    result = engine.evaluate_criteria('309.81', responses)
    assert result['disorder_code'] == '309.81'
    assert result['disorder_name'] == 'Posttraumatic Stress Disorder'
    # PTSD has minimum_criteria of 1 (just trauma exposure), so should be met
    assert result['criteria_met'] == True

def test_dsm5_bipolar_evaluation():
    """Test Bipolar Disorder evaluation"""
    engine = DSM5RuleEngine()
    
    # Test Bipolar I evaluation
    responses = {
        'bp1_1': True,  # Elevated mood (required)
        'bp1_2': True,  # Increased activity (required)
        'bp1_3': True,  # Grandiosity
        'bp1_4': True,  # Decreased sleep
        'bp1_5': True,  # Talkativeness
        'bp1_6': False, # Flight of ideas
        'bp1_7': False, # Distractibility
        'bp1_8': False, # Increased activity
        'bp1_9': False  # Risky behavior
    }
    
    result = engine.evaluate_criteria('296.41', responses)
    assert result['disorder_code'] == '296.41'
    assert result['criteria_met'] == True  # 5 criteria met, need 3
    assert result['met_criteria_count'] == 5

def test_dsm5_ocd_evaluation():
    """Test OCD evaluation"""
    engine = DSM5RuleEngine()
    
    # Test OCD evaluation
    responses = {
        'ocd_1': True,  # Obsessions/compulsions (required)
        'ocd_2': True,  # Persistent thoughts
        'ocd_3': True,  # Attempts to suppress
        'ocd_4': True,  # Repetitive behaviors
        'ocd_5': True,  # Behaviors to reduce anxiety
        'ocd_6': True,  # Time consuming
        'ocd_7': True   # Significant distress (required)
    }
    
    result = engine.evaluate_criteria('300.3', responses)
    assert result['disorder_code'] == '300.3'
    assert result['disorder_name'] == 'Obsessive-Compulsive Disorder'
    assert result['criteria_met'] == True
