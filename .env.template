# Psychiatry EMR Configuration Template
# Copy this file to .env and update the values

# REQUIRED: Generate these values and keep them secret

# Database connection (supports SSL and .pgpass)
DATABASE_URL=postgresql://localhost:5432/psychiatry_emr?sslmode=prefer

# Encryption salt (generate with: python -c "import secrets; print(secrets.token_hex(32))")
ENCRYPTION_SALT=REPLACE_WITH_GENERATED_SALT

# JWT secret key (generate with: python -c "import secrets; print(secrets.token_urlsafe(32))")
SECRET_KEY=REPLACE_WITH_GENERATED_SECRET

# Optional settings
SESSION_TIMEOUT_MINUTES=30
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION_MINUTES=15
DEBUG_MODE=false
LOG_LEVEL=INFO

# Application settings
APP_NAME=Psychiatry EMR
APP_VERSION=1.0.0
AUDIT_LOG_RETENTION_DAYS=2555

# DSM-5-TR configuration
DSM5_CRITERIA_FILE=config/dsm5_criteria.yaml
