#!/bin/bash
# backup.sh - Comprehensive Backup Script for Psychiatry EMR
# HIPAA-compliant encrypted backups with automated scheduling

set -euo pipefail

# Make script executable
chmod +x "$0" 2>/dev/null || true

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BACKUP_DIR="${BACKUP_DIR:-/var/lib/psychiatry-emr/backups}"
RETENTION_DAYS="${BACKUP_RETENTION_DAYS:-30}"
ENCRYPTION_KEY_FILE="${ENCRYPTION_KEY_FILE:-/etc/psychiatry-emr/backup.key}"
LOG_FILE="${LOG_FILE:-/var/log/psychiatry-emr/backup.log}"

# Database configuration
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"
DB_NAME="${POSTGRES_DB:-psychiatry_emr}"
DB_USER="${POSTGRES_USER:-postgres}"
DB_PASSWORD="${POSTGRES_PASSWORD}"

# Logging functions
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${timestamp} [${level}] ${message}" | tee -a "$LOG_FILE"
}

log_info() {
    log "INFO" "$@"
    echo -e "${BLUE}[INFO]${NC} $*"
}

log_success() {
    log "SUCCESS" "$@"
    echo -e "${GREEN}[SUCCESS]${NC} $*"
}

log_warning() {
    log "WARNING" "$@"
    echo -e "${YELLOW}[WARNING]${NC} $*"
}

log_error() {
    log "ERROR" "$@"
    echo -e "${RED}[ERROR]${NC} $*"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking backup prerequisites..."
    
    # Check required commands
    local required_commands=("pg_dump" "gpg" "tar" "gzip")
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            log_error "Required command not found: $cmd"
            exit 1
        fi
    done
    
    # Check backup directory
    if [ ! -d "$BACKUP_DIR" ]; then
        log_info "Creating backup directory: $BACKUP_DIR"
        mkdir -p "$BACKUP_DIR"
    fi
    
    # Check encryption key
    if [ ! -f "$ENCRYPTION_KEY_FILE" ]; then
        log_warning "Encryption key file not found: $ENCRYPTION_KEY_FILE"
        log_info "Generating new encryption key..."
        generate_encryption_key
    fi
    
    # Check database connectivity
    if ! PGPASSWORD="$DB_PASSWORD" pg_isready -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" &> /dev/null; then
        log_error "Cannot connect to database"
        exit 1
    fi
    
    log_success "Prerequisites check completed"
}

# Generate encryption key for backups
generate_encryption_key() {
    local key_dir=$(dirname "$ENCRYPTION_KEY_FILE")
    
    if [ ! -d "$key_dir" ]; then
        mkdir -p "$key_dir"
    fi
    
    # Generate a strong random key
    openssl rand -base64 32 > "$ENCRYPTION_KEY_FILE"
    chmod 600 "$ENCRYPTION_KEY_FILE"
    
    log_success "Encryption key generated: $ENCRYPTION_KEY_FILE"
}

# Create database backup
backup_database() {
    local timestamp="$1"
    local backup_file="$BACKUP_DIR/database_${timestamp}.sql"
    local compressed_file="${backup_file}.gz"
    local encrypted_file="${compressed_file}.gpg"
    
    log_info "Creating database backup..."
    
    # Create database dump
    PGPASSWORD="$DB_PASSWORD" pg_dump \
        -h "$DB_HOST" \
        -p "$DB_PORT" \
        -U "$DB_USER" \
        -d "$DB_NAME" \
        --verbose \
        --no-password \
        --format=custom \
        --compress=9 \
        --file="$backup_file" \
        2>> "$LOG_FILE"
    
    if [ $? -eq 0 ]; then
        log_success "Database dump created: $backup_file"
    else
        log_error "Database dump failed"
        return 1
    fi
    
    # Compress the backup
    log_info "Compressing database backup..."
    gzip "$backup_file"
    
    # Encrypt the compressed backup
    log_info "Encrypting database backup..."
    gpg --cipher-algo AES256 \
        --compress-algo 2 \
        --symmetric \
        --passphrase-file "$ENCRYPTION_KEY_FILE" \
        --batch \
        --yes \
        --output "$encrypted_file" \
        "$compressed_file"
    
    if [ $? -eq 0 ]; then
        # Remove unencrypted file
        rm "$compressed_file"
        log_success "Database backup encrypted: $encrypted_file"
        echo "$encrypted_file"
    else
        log_error "Database backup encryption failed"
        return 1
    fi
}

# Create application files backup
backup_application_files() {
    local timestamp="$1"
    local backup_file="$BACKUP_DIR/application_${timestamp}.tar.gz"
    local encrypted_file="${backup_file}.gpg"
    
    log_info "Creating application files backup..."
    
    # Create list of files to backup
    local files_to_backup=(
        "config/"
        "logs/"
        "uploads/"
        ".env"
        "pyproject.toml"
        "alembic.ini"
    )
    
    # Create tar archive
    cd "$PROJECT_ROOT"
    tar -czf "$backup_file" \
        --exclude="*.pyc" \
        --exclude="__pycache__" \
        --exclude=".git" \
        --exclude="node_modules" \
        --exclude=".venv" \
        "${files_to_backup[@]}" \
        2>> "$LOG_FILE"
    
    if [ $? -eq 0 ]; then
        log_success "Application files archive created: $backup_file"
    else
        log_error "Application files backup failed"
        return 1
    fi
    
    # Encrypt the backup
    log_info "Encrypting application files backup..."
    gpg --cipher-algo AES256 \
        --compress-algo 2 \
        --symmetric \
        --passphrase-file "$ENCRYPTION_KEY_FILE" \
        --batch \
        --yes \
        --output "$encrypted_file" \
        "$backup_file"
    
    if [ $? -eq 0 ]; then
        # Remove unencrypted file
        rm "$backup_file"
        log_success "Application files backup encrypted: $encrypted_file"
        echo "$encrypted_file"
    else
        log_error "Application files backup encryption failed"
        return 1
    fi
}

# Create configuration backup
backup_configuration() {
    local timestamp="$1"
    local backup_file="$BACKUP_DIR/configuration_${timestamp}.tar.gz"
    local encrypted_file="${backup_file}.gpg"
    
    log_info "Creating configuration backup..."
    
    # Create configuration archive
    cd "$PROJECT_ROOT"
    tar -czf "$backup_file" \
        config/ \
        docker-compose*.yml \
        Dockerfile \
        nginx.conf \
        2>> "$LOG_FILE"
    
    if [ $? -eq 0 ]; then
        log_success "Configuration archive created: $backup_file"
    else
        log_error "Configuration backup failed"
        return 1
    fi
    
    # Encrypt the backup
    log_info "Encrypting configuration backup..."
    gpg --cipher-algo AES256 \
        --compress-algo 2 \
        --symmetric \
        --passphrase-file "$ENCRYPTION_KEY_FILE" \
        --batch \
        --yes \
        --output "$encrypted_file" \
        "$backup_file"
    
    if [ $? -eq 0 ]; then
        # Remove unencrypted file
        rm "$backup_file"
        log_success "Configuration backup encrypted: $encrypted_file"
        echo "$encrypted_file"
    else
        log_error "Configuration backup encryption failed"
        return 1
    fi
}

# Create backup manifest
create_backup_manifest() {
    local timestamp="$1"
    local manifest_file="$BACKUP_DIR/manifest_${timestamp}.json"
    local encrypted_manifest="${manifest_file}.gpg"
    
    log_info "Creating backup manifest..."
    
    # Get backup file information
    local db_backup=$(find "$BACKUP_DIR" -name "database_${timestamp}.sql.gz.gpg" -type f)
    local app_backup=$(find "$BACKUP_DIR" -name "application_${timestamp}.tar.gz.gpg" -type f)
    local config_backup=$(find "$BACKUP_DIR" -name "configuration_${timestamp}.tar.gz.gpg" -type f)
    
    # Create manifest JSON
    cat > "$manifest_file" << EOF
{
    "backup_timestamp": "$timestamp",
    "backup_date": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
    "backup_version": "1.0",
    "database_backup": {
        "file": "$(basename "$db_backup")",
        "size": $(stat -c%s "$db_backup" 2>/dev/null || echo 0),
        "checksum": "$(sha256sum "$db_backup" | cut -d' ' -f1)"
    },
    "application_backup": {
        "file": "$(basename "$app_backup")",
        "size": $(stat -c%s "$app_backup" 2>/dev/null || echo 0),
        "checksum": "$(sha256sum "$app_backup" | cut -d' ' -f1)"
    },
    "configuration_backup": {
        "file": "$(basename "$config_backup")",
        "size": $(stat -c%s "$config_backup" 2>/dev/null || echo 0),
        "checksum": "$(sha256sum "$config_backup" | cut -d' ' -f1)"
    },
    "total_size": $(($(stat -c%s "$db_backup" 2>/dev/null || echo 0) + $(stat -c%s "$app_backup" 2>/dev/null || echo 0) + $(stat -c%s "$config_backup" 2>/dev/null || echo 0))),
    "encryption": "AES256",
    "compression": "gzip"
}
EOF
    
    # Encrypt the manifest
    gpg --cipher-algo AES256 \
        --symmetric \
        --passphrase-file "$ENCRYPTION_KEY_FILE" \
        --batch \
        --yes \
        --output "$encrypted_manifest" \
        "$manifest_file"
    
    if [ $? -eq 0 ]; then
        rm "$manifest_file"
        log_success "Backup manifest created: $encrypted_manifest"
    else
        log_error "Backup manifest creation failed"
    fi
}

# Clean old backups
cleanup_old_backups() {
    log_info "Cleaning up old backups (retention: $RETENTION_DAYS days)..."
    
    # Find and remove old backup files
    find "$BACKUP_DIR" -name "*.gpg" -type f -mtime +$RETENTION_DAYS -delete
    
    local removed_count=$(find "$BACKUP_DIR" -name "*.gpg" -type f -mtime +$RETENTION_DAYS | wc -l)
    log_success "Removed $removed_count old backup files"
}

# Verify backup integrity
verify_backup() {
    local backup_file="$1"
    
    log_info "Verifying backup integrity: $(basename "$backup_file")"
    
    # Test GPG decryption without extracting
    if gpg --quiet \
           --batch \
           --passphrase-file "$ENCRYPTION_KEY_FILE" \
           --decrypt "$backup_file" > /dev/null 2>&1; then
        log_success "Backup integrity verified: $(basename "$backup_file")"
        return 0
    else
        log_error "Backup integrity check failed: $(basename "$backup_file")"
        return 1
    fi
}

# Send backup notification
send_backup_notification() {
    local status="$1"
    local details="$2"
    
    if [ -n "${BACKUP_NOTIFICATION_EMAIL:-}" ] && [ -n "${SMTP_SERVER:-}" ]; then
        log_info "Sending backup notification..."
        
        local subject="Psychiatry EMR Backup $status"
        local body="Backup Status: $status\nTimestamp: $(date)\nDetails: $details"
        
        echo -e "$body" | mail -s "$subject" "$BACKUP_NOTIFICATION_EMAIL" || true
    fi
}

# Main backup function
main() {
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_start_time=$(date +%s)
    
    log_info "Starting backup process at $(date)"
    log_info "Backup timestamp: $timestamp"
    
    # Check prerequisites
    check_prerequisites
    
    # Create backups
    local db_backup_file
    local app_backup_file
    local config_backup_file
    
    if db_backup_file=$(backup_database "$timestamp"); then
        verify_backup "$db_backup_file"
    else
        log_error "Database backup failed"
        send_backup_notification "FAILED" "Database backup failed"
        exit 1
    fi
    
    if app_backup_file=$(backup_application_files "$timestamp"); then
        verify_backup "$app_backup_file"
    else
        log_error "Application files backup failed"
        send_backup_notification "FAILED" "Application files backup failed"
        exit 1
    fi
    
    if config_backup_file=$(backup_configuration "$timestamp"); then
        verify_backup "$config_backup_file"
    else
        log_error "Configuration backup failed"
        send_backup_notification "FAILED" "Configuration backup failed"
        exit 1
    fi
    
    # Create manifest
    create_backup_manifest "$timestamp"
    
    # Cleanup old backups
    cleanup_old_backups
    
    # Calculate backup duration
    local backup_end_time=$(date +%s)
    local backup_duration=$((backup_end_time - backup_start_time))
    
    log_success "Backup process completed successfully"
    log_info "Backup duration: ${backup_duration} seconds"
    log_info "Backup files created in: $BACKUP_DIR"
    
    # Send success notification
    send_backup_notification "SUCCESS" "All backups completed successfully in ${backup_duration} seconds"
}

# Handle script arguments
case "${1:-}" in
    --verify)
        if [ -z "${2:-}" ]; then
            log_error "Please specify backup file to verify"
            exit 1
        fi
        verify_backup "$2"
        ;;
    --cleanup)
        cleanup_old_backups
        ;;
    --help)
        echo "Usage: $0 [--verify <backup_file>] [--cleanup] [--help]"
        echo "  --verify <file>  Verify backup file integrity"
        echo "  --cleanup        Clean up old backup files"
        echo "  --help           Show this help message"
        exit 0
        ;;
    "")
        main
        ;;
    *)
        log_error "Unknown option: $1"
        echo "Use --help for usage information"
        exit 1
        ;;
esac
