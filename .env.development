# Psychiatry EMR Development Configuration
# This file contains development-safe defaults

# Database Configuration
POSTGRES_DB=psychiatry_emr_dev
POSTGRES_USER=postgres
POSTGRES_PASSWORD=dev_password_123

# Redis Configuration
REDIS_PASSWORD=dev_redis_123

# Application Database URL
DATABASE_URL=postgresql://postgres:dev_password_123@localhost:5432/psychiatry_emr_dev

# Encryption Configuration (Development Only - DO NOT USE IN PRODUCTION)
ENCRYPTION_SALT=a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456
SECRET_KEY=dev_secret_key_for_development_only_32chars

# Application Settings
APP_NAME=Psychiatry EMR (Development)
APP_VERSION=1.0.0-dev
DEBUG_MODE=true
LOG_LEVEL=DEBUG
LOG_FILE=psychiatry_emr_dev.log

# Security Settings (Relaxed for development)
SESSION_TIMEOUT_MINUTES=60
MAX_LOGIN_ATTEMPTS=10
LOCKOUT_DURATION_MINUTES=5
AUDIT_LOG_RETENTION_DAYS=30

# Database Connection Pool Settings
DB_POOL_SIZE=3
DB_MAX_OVERFLOW=5

# DSM-5-TR Configuration
DSM5_CRITERIA_FILE=config/dsm5_criteria.yaml

# Development Features
ENABLE_DEBUG_TOOLBAR=true
ENABLE_SQL_LOGGING=true
SKIP_ENCRYPTION_VALIDATION=false
