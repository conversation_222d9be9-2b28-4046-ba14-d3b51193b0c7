#!/bin/bash
# setup_dev.sh - Enhanced development environment setup

set -e  # Exit on any error

echo "🏥 Setting up Psychiatry EMR Development Environment"

# Check prerequisites
command -v python3 >/dev/null 2>&1 || { echo "❌ Python 3 is required but not installed."; exit 1; }
command -v psql >/dev/null 2>&1 || { echo "❌ PostgreSQL client is required but not installed."; exit 1; }

# Create virtual environment
echo "📦 Creating virtual environment..."
python3 -m venv venv
source venv/bin/activate

# Install dependencies
echo "📚 Installing dependencies..."
pip install --upgrade pip
pip install -r requirements.txt
pip install -r requirements-dev.txt

# Setup pre-commit hooks
echo "🔧 Setting up pre-commit hooks..."
pre-commit install

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "⚙️ Creating .env file..."
    python3 -c "
from config.settings import generate_env_template
with open('.env', 'w') as f:
    f.write(generate_env_template())
print('✅ .env file created with secure defaults')
"
fi

# Database setup
DB_NAME=${DB_NAME:-psychiatry_emr_dev}
echo "🗄️ Setting up database: $DB_NAME"

# Check if database exists
if psql -lqt | cut -d \| -f 1 | grep -qw $DB_NAME; then
    echo "ℹ️ Database $DB_NAME already exists"
else
    createdb $DB_NAME
    echo "✅ Database $DB_NAME created"
fi

# Enable extensions
psql -d $DB_NAME -c "CREATE EXTENSION IF NOT EXISTS pgcrypto;" -q
psql -d $DB_NAME -c "CREATE EXTENSION IF NOT EXISTS pg_trgm;" -q  # For text search
echo "✅ Database extensions enabled"

# Create directories
mkdir -p scripts reports backups logs

# Apply custom indexes
echo "📊 Creating performance indexes..."
psql -d $DB_NAME -f sql/performance_indexes.sql -q

echo "✅ Development environment setup complete!"
echo ""
echo "Next steps:"
echo "1. source venv/bin/activate"
echo "2. Enter master password when prompted: python main.py"
echo "3. Open http://localhost:3000"
echo ""
echo "🧪 Run tests with: pytest"
echo "🎨 Format code with: black ."
echo "🔍 Lint code with: ruff check ."
