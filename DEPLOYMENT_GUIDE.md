# Psychiatry EMR - Production Deployment Guide

## 🚀 Production-Ready Deployment

This guide provides step-by-step instructions for deploying the Psychiatry EMR application in a production environment.

## ✅ Pre-Deployment Checklist

### System Requirements
- **Python**: 3.11 or higher
- **Database**: PostgreSQL 13+ (recommended) or SQLite for development
- **Memory**: Minimum 2GB RAM, 4GB+ recommended
- **Storage**: Minimum 10GB free space
- **Network**: HTTPS-capable web server (nginx/Apache recommended)

### Security Requirements
- SSL/TLS certificate for HTTPS
- Firewall configured to allow only necessary ports
- Regular security updates applied
- Backup strategy implemented

## 📋 Installation Steps

### 1. Environment Setup

```bash
# Create application directory
sudo mkdir -p /opt/psychiatry-emr
cd /opt/psychiatry-emr

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Database Configuration

#### PostgreSQL Setup (Recommended)
```bash
# Install PostgreSQL
sudo apt-get install postgresql postgresql-contrib

# Create database and user
sudo -u postgres psql
CREATE DATABASE psychiatry_emr;
CREATE USER emr_user WITH PASSWORD 'secure_password_here';
GRANT ALL PRIVILEGES ON DATABASE psychiatry_emr TO emr_user;
\q
```

#### Environment Configuration
```bash
# Copy and configure environment file
cp .env.example .env
nano .env
```

### 3. Environment Variables (.env)

```bash
# Database Configuration
DATABASE_URL=postgresql://emr_user:secure_password@localhost/psychiatry_emr

# Security Configuration (GENERATE NEW VALUES!)
ENCRYPTION_SALT=<generate_with_python_secrets_token_hex_32>
SECRET_KEY=<generate_with_python_secrets_token_urlsafe_32>

# Application Settings
APP_NAME=Psychiatry EMR
APP_VERSION=1.0.0
DEBUG_MODE=false
LOG_LEVEL=INFO

# Session Configuration
SESSION_TIMEOUT_MINUTES=30
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION_MINUTES=15

# Audit Configuration
AUDIT_LOG_RETENTION_DAYS=2555

# DSM-5 Configuration
DSM5_CRITERIA_FILE=config/dsm5_criteria.yaml
```

### 4. Security Key Generation

```bash
# Generate encryption salt
python3 -c "import secrets; print('ENCRYPTION_SALT=' + secrets.token_hex(32))"

# Generate secret key
python3 -c "import secrets; print('SECRET_KEY=' + secrets.token_urlsafe(32))"
```

### 5. Database Initialization

```bash
# Set master password for encryption
export MASTER_PASSWORD="your_secure_master_password"

# Initialize database
python3 -c "
from services.database import initialize_database
initialize_database()
print('Database initialized successfully')
"

# Create admin user
python3 create_admin_user.py
```

### 6. Application Testing

```bash
# Run production readiness tests
python3 test_suite.py

# Test application components
python3 -c "
import os
os.environ['MASTER_PASSWORD'] = 'your_master_password'
from main import check_health
print(check_health())
"
```

## 🔧 Production Configuration

### Web Server Setup (nginx)

```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /path/to/ssl/cert.pem;
    ssl_certificate_key /path/to/ssl/private.key;
    
    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### Systemd Service

```ini
[Unit]
Description=Psychiatry EMR Application
After=network.target postgresql.service

[Service]
Type=simple
User=emr
Group=emr
WorkingDirectory=/opt/psychiatry-emr
Environment=MASTER_PASSWORD=your_secure_master_password
ExecStart=/opt/psychiatry-emr/venv/bin/python main.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

## 🔐 Security Best Practices

### 1. Access Control
- Use strong passwords for all accounts
- Implement role-based access control
- Regular password rotation policy
- Multi-factor authentication (recommended)

### 2. Data Protection
- All PHI data is encrypted at rest
- Secure transmission over HTTPS only
- Regular security audits
- Compliance with HIPAA requirements

### 3. Monitoring
- Enable comprehensive audit logging
- Monitor failed login attempts
- Set up alerts for suspicious activity
- Regular backup verification

## 📊 Monitoring and Maintenance

### Health Checks
```bash
# Application health check
curl -k https://your-domain.com/health

# Database connectivity
python3 -c "
from services.database import get_engine
from sqlmodel import text
engine = get_engine()
with engine.connect() as conn:
    result = conn.execute(text('SELECT 1'))
    print('Database: OK' if result.fetchone()[0] == 1 else 'Database: ERROR')
"
```

### Log Monitoring
```bash
# Application logs
tail -f /var/log/psychiatry-emr/app.log

# Audit logs (check database)
python3 -c "
from services.database import get_engine
from sqlmodel import text
engine = get_engine()
with engine.connect() as conn:
    result = conn.execute(text('SELECT COUNT(*) FROM auditlog WHERE timestamp > NOW() - INTERVAL 1 DAY'))
    print(f'Audit entries today: {result.fetchone()[0]}')
"
```

## 🔄 Backup and Recovery

### Database Backup
```bash
# Daily backup script
pg_dump psychiatry_emr > backup_$(date +%Y%m%d).sql

# Automated backup with retention
#!/bin/bash
BACKUP_DIR="/opt/backups/psychiatry-emr"
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump psychiatry_emr | gzip > "$BACKUP_DIR/backup_$DATE.sql.gz"
find "$BACKUP_DIR" -name "backup_*.sql.gz" -mtime +30 -delete
```

### Application Backup
```bash
# Backup configuration and data
tar -czf emr_backup_$(date +%Y%m%d).tar.gz \
    /opt/psychiatry-emr/.env \
    /opt/psychiatry-emr/config/ \
    /opt/psychiatry-emr/psychiatry_emr.db
```

## 🚨 Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check PostgreSQL service status
   - Verify connection string in .env
   - Check firewall settings

2. **Encryption Errors**
   - Verify ENCRYPTION_SALT is set correctly
   - Check MASTER_PASSWORD environment variable
   - Ensure consistent encryption keys

3. **Authentication Issues**
   - Check user exists in database
   - Verify password hashing
   - Check session timeout settings

### Support Contacts
- **Technical Support**: [Your support contact]
- **Security Issues**: [Your security contact]
- **Emergency**: [Your emergency contact]

---

## 📞 User Access Instructions

### Default Admin Access
- **URL**: https://your-domain.com
- **Username**: admin
- **Password**: admin123 (CHANGE IMMEDIATELY)

### First-Time Setup
1. Log in with admin credentials
2. Change default password
3. Create additional user accounts
4. Configure system settings
5. Import patient data (if migrating)

### User Training
- Provide HIPAA compliance training
- System usage documentation
- Security best practices
- Emergency procedures

---

**🎉 Congratulations! Your Psychiatry EMR system is now production-ready!**
