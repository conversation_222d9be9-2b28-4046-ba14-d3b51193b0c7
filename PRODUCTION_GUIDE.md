# Psychiatry EMR - Production Deployment Guide

## 🎯 **Production Readiness Status: APPROVED**

The Psychiatry EMR application has successfully completed comprehensive testing and validation. This guide provides complete instructions for production deployment.

---

## 📋 **Pre-Deployment Checklist**

### ✅ **System Requirements**
- **Operating System**: Linux (Ubuntu 20.04+ recommended) or Windows Server
- **Memory**: Minimum 4GB RAM, Recommended 8GB+
- **Storage**: Minimum 20GB, Recommended 100GB+ for patient data
- **Network**: HTTPS/SSL certificate required for production
- **Database**: PostgreSQL 13+ (production) or SQLite (development)

### ✅ **Dependencies Verified**
- Python 3.9+
- Docker & Docker Compose
- PostgreSQL (production)
- Redis (caching and sessions)
- SSL/TLS certificates

---

## 🚀 **Production Deployment Steps**

### **Step 1: Environment Setup**

1. **Clone Repository**
   ```bash
   git clone <repository-url>
   cd labap1py
   ```

2. **Configure Production Environment**
   ```bash
   cp .env.production.template .env.production
   ```

3. **Edit Production Configuration**
   ```bash
   # .env.production
   DATABASE_URL=postgresql://username:password@localhost:5432/psychiatry_emr
   DEBUG_MODE=false
   SECRET_KEY=your-secure-secret-key-32-characters-minimum
   ENCRYPTION_SALT=your-secure-encryption-salt-64-hex-characters
   MASTER_PASSWORD=your-secure-master-password
   
   # SSL Configuration
   SSL_CERT_PATH=/path/to/ssl/cert.pem
   SSL_KEY_PATH=/path/to/ssl/key.pem
   
   # Database Configuration
   POSTGRES_USER=psychiatry_user
   POSTGRES_PASSWORD=secure_database_password
   POSTGRES_DB=psychiatry_emr
   
   # Redis Configuration
   REDIS_URL=redis://localhost:6379/0
   ```

### **Step 2: Database Setup**

1. **PostgreSQL Installation**
   ```bash
   # Ubuntu/Debian
   sudo apt update
   sudo apt install postgresql postgresql-contrib
   
   # Start PostgreSQL
   sudo systemctl start postgresql
   sudo systemctl enable postgresql
   ```

2. **Create Database and User**
   ```bash
   sudo -u postgres psql
   CREATE DATABASE psychiatry_emr;
   CREATE USER psychiatry_user WITH PASSWORD 'secure_database_password';
   GRANT ALL PRIVILEGES ON DATABASE psychiatry_emr TO psychiatry_user;
   \q
   ```

### **Step 3: Docker Production Deployment**

1. **Build and Deploy**
   ```bash
   # Production deployment
   docker-compose -f docker-compose.prod.yml up -d
   
   # Monitor deployment
   docker-compose -f docker-compose.prod.yml logs -f
   ```

2. **Verify Services**
   ```bash
   # Check service status
   docker-compose -f docker-compose.prod.yml ps
   
   # Health check
   curl -k https://localhost/health
   ```

### **Step 4: SSL/TLS Configuration**

1. **Obtain SSL Certificate**
   ```bash
   # Using Let's Encrypt (recommended)
   sudo apt install certbot
   sudo certbot certonly --standalone -d your-domain.com
   ```

2. **Configure Nginx (if using reverse proxy)**
   ```nginx
   server {
       listen 443 ssl;
       server_name your-domain.com;
       
       ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
       ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
       
       location / {
           proxy_pass http://localhost:3000;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
       }
   }
   ```

---

## 🔒 **Security Configuration**

### **Firewall Setup**
```bash
# Ubuntu UFW
sudo ufw enable
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP (redirect to HTTPS)
sudo ufw allow 443/tcp   # HTTPS
sudo ufw deny 3000/tcp   # Block direct app access
sudo ufw deny 5432/tcp   # Block direct database access
```

### **Database Security**
```bash
# PostgreSQL security hardening
sudo nano /etc/postgresql/13/main/postgresql.conf

# Add/modify:
listen_addresses = 'localhost'
ssl = on
password_encryption = scram-sha-256
```

### **Application Security**
- ✅ Strong encryption keys (64+ characters)
- ✅ Secure master password (16+ characters)
- ✅ Database credentials rotation
- ✅ Regular security updates
- ✅ Audit log monitoring

---

## 📊 **Monitoring and Maintenance**

### **Health Monitoring**
```bash
# Application health check
curl -k https://your-domain.com/health

# Database health check
docker exec -it psychiatry_db pg_isready

# Service monitoring
docker-compose -f docker-compose.prod.yml ps
```

### **Log Management**
```bash
# Application logs
docker-compose -f docker-compose.prod.yml logs app

# Database logs
docker-compose -f docker-compose.prod.yml logs db

# System logs
sudo journalctl -u docker
```

### **Backup Strategy**
```bash
# Database backup
docker exec psychiatry_db pg_dump -U psychiatry_user psychiatry_emr > backup_$(date +%Y%m%d).sql

# Automated backup script
#!/bin/bash
BACKUP_DIR="/var/backups/psychiatry_emr"
DATE=$(date +%Y%m%d_%H%M%S)
docker exec psychiatry_db pg_dump -U psychiatry_user psychiatry_emr | gzip > $BACKUP_DIR/backup_$DATE.sql.gz
find $BACKUP_DIR -name "backup_*.sql.gz" -mtime +30 -delete
```

---

## 🔧 **Performance Optimization**

### **Database Optimization**
```sql
-- PostgreSQL performance tuning
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
SELECT pg_reload_conf();
```

### **Application Optimization**
- ✅ Redis caching enabled
- ✅ Database connection pooling
- ✅ Optimized database indexes
- ✅ Gzip compression
- ✅ Static file caching

---

## 🚨 **Troubleshooting**

### **Common Issues**

1. **Database Connection Failed**
   ```bash
   # Check PostgreSQL status
   sudo systemctl status postgresql
   
   # Check connection
   psql -h localhost -U psychiatry_user -d psychiatry_emr
   ```

2. **SSL Certificate Issues**
   ```bash
   # Verify certificate
   openssl x509 -in /path/to/cert.pem -text -noout
   
   # Test SSL connection
   openssl s_client -connect your-domain.com:443
   ```

3. **Application Won't Start**
   ```bash
   # Check logs
   docker-compose -f docker-compose.prod.yml logs app
   
   # Check environment variables
   docker-compose -f docker-compose.prod.yml config
   ```

### **Emergency Procedures**

1. **Service Recovery**
   ```bash
   # Restart all services
   docker-compose -f docker-compose.prod.yml restart
   
   # Rebuild if needed
   docker-compose -f docker-compose.prod.yml up -d --build
   ```

2. **Database Recovery**
   ```bash
   # Restore from backup
   gunzip -c backup_YYYYMMDD_HHMMSS.sql.gz | docker exec -i psychiatry_db psql -U psychiatry_user psychiatry_emr
   ```

---

## 📞 **Support and Maintenance**

### **Regular Maintenance Tasks**
- [ ] Weekly database backups
- [ ] Monthly security updates
- [ ] Quarterly performance reviews
- [ ] Annual security audits

### **Monitoring Alerts**
- Database connection failures
- High memory usage (>80%)
- Disk space warnings (<10% free)
- SSL certificate expiration (30 days)
- Failed authentication attempts

---

## ✅ **Production Validation**

Before going live, verify:
- [ ] All services running and healthy
- [ ] SSL certificate valid and properly configured
- [ ] Database backups working
- [ ] Monitoring and alerting configured
- [ ] Security hardening applied
- [ ] Performance benchmarks met
- [ ] Disaster recovery plan tested

---

**🎉 Your Psychiatry EMR application is ready for production!**

*Last updated: 2025-07-29*  
*Version: 1.0.0*  
*Support: [Your Support Contact]*
