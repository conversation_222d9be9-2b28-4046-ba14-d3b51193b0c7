"""
Psychiatry EMR - Authentication Service
User authentication with session management and security monitoring.
"""

import reflex as rx
from sqlmodel import Session, select
from typing import Optional, Dict, Any
import logging
import hashlib
import secrets
import jwt
from datetime import datetime, timedelta

from models.user import User, UserSession
from services.database import get_db_session
from services.audit_service import AuditService

logger = logging.getLogger(__name__)

class AuthService:
    """Authentication service with session management"""
    
    def __init__(self, db_session: Session):
        self.db = db_session
        self.audit_service = AuditService(db_session, 0)  # System user for auth events
    
    def authenticate_user(self, username: str, password: str, ip_address: str = None, user_agent: str = None) -> Optional[Dict[str, Any]]:
        """Authenticate user and create session"""
        try:
            # Find user by username
            user = self.db.exec(
                select(User).where(User.username == username, User.is_active == True)
            ).first()
            
            if not user:
                self.audit_service.log_authentication("LOGIN_FAILED", success=False, error_message="User not found")
                logger.warning(f"Login attempt for non-existent user: {username}")
                return None
            
            # Verify password
            if not self._verify_password(password, user.password_hash):
                self.audit_service.log_authentication("LOGIN_FAILED", success=False, error_message="Invalid password")
                logger.warning(f"Invalid password for user: {username}")
                return None
            
            # Create session
            session_token = self._generate_session_token()
            session = UserSession(
                user_id=user.id,
                session_token=session_token,
                ip_address=ip_address,
                user_agent=user_agent,
                expires_at=datetime.utcnow() + timedelta(hours=8)  # 8-hour sessions
            )
            
            self.db.add(session)
            self.db.commit()
            
            # Update user last login
            user.last_login = datetime.utcnow()
            self.db.commit()
            
            # Audit successful login
            self.audit_service.log_authentication("LOGIN_SUCCESS", success=True)
            
            logger.info(f"User authenticated successfully: {username}")
            
            return {
                "user_id": user.id,
                "username": user.username,
                "full_name": user.full_name,
                "role": user.role,
                "session_token": session_token,
                "expires_at": session.expires_at.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            self.audit_service.log_authentication("LOGIN_ERROR", success=False, error_message=str(e))
            return None
    
    def validate_session(self, session_token: str) -> Optional[Dict[str, Any]]:
        """Validate session token and return user info"""
        try:
            session = self.db.exec(
                select(UserSession).where(
                    UserSession.session_token == session_token,
                    UserSession.is_active == True,
                    UserSession.expires_at > datetime.utcnow()
                )
            ).first()
            
            if not session:
                return None
            
            # Get user
            user = self.db.get(User, session.user_id)
            if not user or not user.is_active:
                return None
            
            # Update session last activity
            session.last_activity = datetime.utcnow()
            self.db.commit()
            
            return {
                "user_id": user.id,
                "username": user.username,
                "full_name": user.full_name,
                "role": user.role,
                "session_id": session.id
            }
            
        except Exception as e:
            logger.error(f"Session validation error: {e}")
            return None
    
    def logout_user(self, session_token: str) -> bool:
        """Logout user by invalidating session"""
        try:
            session = self.db.exec(
                select(UserSession).where(UserSession.session_token == session_token)
            ).first()
            
            if session:
                session.is_active = False
                session.logged_out_at = datetime.utcnow()
                self.db.commit()
                
                # Audit logout
                self.audit_service.log_authentication("LOGOUT", success=True)
                
                logger.info(f"User logged out: session {session.id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Logout error: {e}")
            return False
    
    def create_user(self, username: str, password: str, full_name: str, role: str = "clinician") -> Optional[User]:
        """Create new user account"""
        try:
            # Check if username exists
            existing_user = self.db.exec(
                select(User).where(User.username == username)
            ).first()
            
            if existing_user:
                logger.warning(f"Attempt to create duplicate user: {username}")
                return None
            
            # Hash password
            password_hash = self._hash_password(password)
            
            # Create user
            user = User(
                username=username,
                password_hash=password_hash,
                full_name=full_name,
                role=role
            )
            
            self.db.add(user)
            self.db.commit()
            self.db.refresh(user)
            
            # Audit user creation
            self.audit_service.log_action("CREATE", "user", user.id, 
                new_values={"username": username, "full_name": full_name, "role": role})
            
            logger.info(f"User created successfully: {username}")
            return user
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"User creation error: {e}")
            return None
    
    def change_password(self, user_id: int, old_password: str, new_password: str) -> bool:
        """Change user password"""
        try:
            user = self.db.get(User, user_id)
            if not user:
                return False
            
            # Verify old password
            if not self._verify_password(old_password, user.password_hash):
                self.audit_service.log_action("PASSWORD_CHANGE_FAILED", "user", user_id, 
                    success=False, error_message="Invalid old password")
                return False
            
            # Update password
            user.password_hash = self._hash_password(new_password)
            user.password_changed_at = datetime.utcnow()
            self.db.commit()
            
            # Audit password change
            self.audit_service.log_action("PASSWORD_CHANGED", "user", user_id)
            
            logger.info(f"Password changed for user: {user_id}")
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Password change error: {e}")
            return False
    
    def _hash_password(self, password: str) -> str:
        """Hash password using secure method"""
        salt = secrets.token_hex(32)
        password_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
        return f"{salt}:{password_hash.hex()}"
    
    def _verify_password(self, password: str, stored_hash: str) -> bool:
        """Verify password against stored hash"""
        try:
            salt, hash_hex = stored_hash.split(':')
            password_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
            return password_hash.hex() == hash_hex
        except:
            return False
    
    def _generate_session_token(self) -> str:
        """Generate secure session token"""
        return secrets.token_urlsafe(32)
    
    def cleanup_expired_sessions(self):
        """Clean up expired sessions"""
        try:
            expired_sessions = self.db.exec(
                select(UserSession).where(UserSession.expires_at < datetime.utcnow())
            ).all()
            
            for session in expired_sessions:
                session.is_active = False
            
            self.db.commit()
            
            if expired_sessions:
                logger.info(f"Cleaned up {len(expired_sessions)} expired sessions")
                
        except Exception as e:
            logger.error(f"Session cleanup error: {e}")
