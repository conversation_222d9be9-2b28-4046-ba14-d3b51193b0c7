-- Psychiatry EMR - Performance Indexes (SQLite Compatible)
-- Performance-optimized indexes for the Psychiatry EMR - SQLite version
-- Note: SQLite has limited index types compared to PostgreSQL

-- Patient table indexes
CREATE INDEX IF NOT EXISTS idx_patient_dob ON patient(dob);
CREATE INDEX IF NOT EXISTS idx_patient_created_at ON patient(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_patient_active ON patient(is_active);
CREATE INDEX IF NOT EXISTS idx_patient_merged_into ON patient(merged_into);

-- Composite index for name + DOB uniqueness checking
CREATE INDEX IF NOT EXISTS idx_patient_name_dob ON patient(name_encrypted, dob);

-- Audit log indexes for performance monitoring
CREATE INDEX IF NOT EXISTS idx_audit_user_ts ON audit_log(user_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_audit_table_record ON audit_log(table_name, record_id);
CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_audit_failed ON audit_log(success, timestamp DESC);

-- User access indexes
CREATE INDEX IF NOT EXISTS idx_user_access_patient ON user_patient_access(patient_id);
CREATE INDEX IF NOT EXISTS idx_user_access_user ON user_patient_access(user_id);
CREATE INDEX IF NOT EXISTS idx_user_access_granted ON user_patient_access(granted_at DESC);

-- Clinical data indexes
CREATE INDEX IF NOT EXISTS idx_present_illness_patient ON present_illness(patient_id);
CREATE INDEX IF NOT EXISTS idx_present_illness_date ON present_illness(assessment_date DESC);
CREATE INDEX IF NOT EXISTS idx_present_illness_diagnosis ON present_illness(primary_diagnosis);

-- Security monitoring indexes for bulk access detection
CREATE INDEX IF NOT EXISTS idx_audit_log_bulk_access ON audit_log(user_id, timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_log_patient_access ON audit_log(user_id, record_id, timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_log_authentication ON audit_log(user_id, timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_log_dsm5_evaluations ON audit_log(timestamp);

-- Indexes for RLS performance (simplified for SQLite)
CREATE INDEX IF NOT EXISTS idx_user_patient_access_rls ON user_patient_access(user_id, patient_id);
CREATE INDEX IF NOT EXISTS idx_present_illness_patient_rls ON present_illness(patient_id);

-- Update table statistics (SQLite equivalent)
ANALYZE;