# tests/performance/test_encryption_performance.py
import pytest
import time
from security.encryption import EncryptionService, generate_salt

def test_encryption_performance(benchmark):
    """Benchmark encryption performance"""
    salt = generate_salt()
    service = EncryptionService("test_password", salt)
    test_data = "John Doe - 123 Main St, Anytown, USA - <EMAIL>"
    
    result = benchmark(service.encrypt, test_data)
    assert len(result) > 0

def test_decryption_performance(benchmark):
    """Benchmark decryption performance"""
    salt = generate_salt()
    service = EncryptionService("test_password", salt)
    test_data = "John Doe - 123 Main St, Anytown, USA - <EMAIL>"
    encrypted = service.encrypt(test_data)
    
    result = benchmark(service.decrypt, encrypted)
    assert result == test_data

def test_bulk_encryption_performance():
    """Test encryption performance with bulk data"""
    salt = generate_salt()
    service = EncryptionService("test_password", salt)
    
    # Test data of various sizes
    test_cases = [
        ("<PERSON>", "John Doe"),
        ("<PERSON>", "John Doe - 123 Main Street, Anytown, State 12345, USA"),
        ("Large", "A" * 1000),  # 1KB of data
        ("XLarge", "B" * 10000)  # 10KB of data
    ]
    
    results = {}
    
    for size_name, data in test_cases:
        start_time = time.time()
        
        # Encrypt 100 times
        for _ in range(100):
            encrypted = service.encrypt(data)
            decrypted = service.decrypt(encrypted)
            assert decrypted == data
        
        end_time = time.time()
        avg_time = (end_time - start_time) / 100
        results[size_name] = avg_time
        
        print(f"{size_name} data ({len(data)} chars): {avg_time:.4f}s per operation")
    
    # Verify performance is reasonable (less than 10ms per operation)
    for size_name, avg_time in results.items():
        assert avg_time < 0.01, f"{size_name} encryption too slow: {avg_time:.4f}s"

def test_concurrent_encryption_performance():
    """Test encryption performance under concurrent load"""
    import threading
    import queue
    
    salt = generate_salt()
    service = EncryptionService("test_password", salt)
    test_data = "Concurrent test data for encryption performance"
    
    results_queue = queue.Queue()
    num_threads = 10
    operations_per_thread = 50
    
    def encrypt_worker():
        thread_results = []
        for _ in range(operations_per_thread):
            start_time = time.time()
            encrypted = service.encrypt(test_data)
            decrypted = service.decrypt(encrypted)
            end_time = time.time()
            
            assert decrypted == test_data
            thread_results.append(end_time - start_time)
        
        results_queue.put(thread_results)
    
    # Start threads
    threads = []
    start_time = time.time()
    
    for _ in range(num_threads):
        thread = threading.Thread(target=encrypt_worker)
        thread.start()
        threads.append(thread)
    
    # Wait for completion
    for thread in threads:
        thread.join()
    
    end_time = time.time()
    total_time = end_time - start_time
    
    # Collect results
    all_times = []
    while not results_queue.empty():
        thread_times = results_queue.get()
        all_times.extend(thread_times)
    
    # Calculate statistics
    total_operations = num_threads * operations_per_thread
    avg_time = sum(all_times) / len(all_times)
    max_time = max(all_times)
    min_time = min(all_times)
    
    print(f"Concurrent encryption test:")
    print(f"  Total operations: {total_operations}")
    print(f"  Total time: {total_time:.2f}s")
    print(f"  Operations per second: {total_operations / total_time:.2f}")
    print(f"  Average time per operation: {avg_time:.4f}s")
    print(f"  Min time: {min_time:.4f}s")
    print(f"  Max time: {max_time:.4f}s")
    
    # Performance assertions
    assert avg_time < 0.01, f"Average encryption time too slow: {avg_time:.4f}s"
    assert max_time < 0.05, f"Maximum encryption time too slow: {max_time:.4f}s"
    assert total_operations / total_time > 100, "Throughput too low"

def test_memory_usage_encryption():
    """Test memory usage during encryption operations"""
    import psutil
    import os
    
    salt = generate_salt()
    service = EncryptionService("test_password", salt)
    
    # Get initial memory usage
    process = psutil.Process(os.getpid())
    initial_memory = process.memory_info().rss / 1024 / 1024  # MB
    
    # Perform many encryption operations
    large_data = "X" * 10000  # 10KB
    encrypted_data = []
    
    for i in range(1000):
        encrypted = service.encrypt(f"{large_data}_{i}")
        encrypted_data.append(encrypted)
    
    # Check memory usage
    peak_memory = process.memory_info().rss / 1024 / 1024  # MB
    memory_increase = peak_memory - initial_memory
    
    print(f"Memory usage test:")
    print(f"  Initial memory: {initial_memory:.2f} MB")
    print(f"  Peak memory: {peak_memory:.2f} MB")
    print(f"  Memory increase: {memory_increase:.2f} MB")
    
    # Decrypt all data to verify integrity
    for i, encrypted in enumerate(encrypted_data):
        decrypted = service.decrypt(encrypted)
        expected = f"{large_data}_{i}"
        assert decrypted == expected
    
    # Memory increase should be reasonable (less than 100MB for this test)
    assert memory_increase < 100, f"Memory usage too high: {memory_increase:.2f} MB"

def test_encryption_key_derivation_performance():
    """Test performance of key derivation (PBKDF2)"""
    passwords = ["short", "medium_length_password", "very_long_password_with_many_characters_to_test_performance"]
    
    for password in passwords:
        start_time = time.time()
        
        # Key derivation happens during service initialization
        salt = generate_salt()
        service = EncryptionService(password, salt)
        
        end_time = time.time()
        derivation_time = end_time - start_time
        
        print(f"Key derivation for '{password[:10]}...' took {derivation_time:.4f}s")
        
        # Key derivation should complete within reasonable time (less than 1 second)
        assert derivation_time < 1.0, f"Key derivation too slow: {derivation_time:.4f}s"
        
        # Verify the service works
        test_data = "Test encryption after key derivation"
        encrypted = service.encrypt(test_data)
        decrypted = service.decrypt(encrypted)
        assert decrypted == test_data
