# Security & Encryption Implementation

## Multi-Layer Security Architecture

### Core Encryption Service

```python
# security/encryption.py
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import os
import secrets
import logging
from typing import Protocol
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)

class CryptoInterface(Protocol):
    """Interface for encryption operations"""
    def encrypt(self, data: str) -> str: ...
    def decrypt(self, encrypted_data: str) -> str: ...

class EncryptionService:
    """Explicit encryption service for PHI data"""
    
    def __init__(self, master_password: str, salt: bytes):
        """Initialize with master password and required salt"""
        self._derive_key(master_password, salt)
    
    def _derive_key(self, password: str, salt: bytes) -> None:
        """Derive encryption key from master password and salt"""
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        self.cipher = Fernet(key)
    
    def encrypt(self, data: str) -> str:
        """Encrypt sensitive data"""
        if not data:
            return ""
        try:
            encrypted = self.cipher.encrypt(data.encode())
            logger.debug(f"Successfully encrypted data of length {len(data)}")
            return base64.urlsafe_b64encode(encrypted).decode()
        except Exception as e:
            logger.error(f"Encryption failed: {e}")
            raise
    
    def decrypt(self, encrypted_data: str) -> str:
        """Decrypt sensitive data"""
        if not encrypted_data:
            return ""
        try:
            decoded = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted = self.cipher.decrypt(decoded)
            logger.debug(f"Successfully decrypted data")
            return decrypted.decode()
        except Exception as e:
            logger.error(f"Decryption failed: {e}")
            raise

def generate_salt() -> bytes:
    """Generate a cryptographically secure salt"""
    return secrets.token_bytes(32)

def initialize_encryption(master_password: str) -> EncryptionService:
    """Initialize encryption service with master password"""
    # Get salt from environment - MUST be set, no default
    salt_hex = os.environ.get('ENCRYPTION_SALT')
    if not salt_hex:
        raise ValueError("ENCRYPTION_SALT environment variable must be set")
    
    try:
        salt = bytes.fromhex(salt_hex)
    except ValueError:
        raise ValueError("ENCRYPTION_SALT must be a valid hex string")
    
    service = EncryptionService(master_password, salt)
    logger.info("Encryption service initialized")
    return service
```

## Configuration Management

### Enhanced Settings with Validation

```python
# config/settings.py
from pydantic import BaseSettings, validator
from typing import Optional
import os
import secrets

class Settings(BaseSettings):
    """Application settings with validation"""
    
    # Database - support full PostgreSQL connection strings
    database_url: str = "postgresql://localhost:5432/psychiatry_emr"
    db_pool_size: int = 5
    db_max_overflow: int = 10
    
    # Security - REQUIRED settings, no defaults
    encryption_salt: str  # MUST be set in .env, no default
    secret_key: str  # JWT signing key for Reflex auth
    session_timeout_minutes: int = 30
    max_login_attempts: int = 5
    lockout_duration_minutes: int = 15
    
    # Application
    app_name: str = "Psychiatry EMR"
    app_version: str = "1.0.0"
    debug_mode: bool = False
    
    # Logging
    log_level: str = "INFO"
    log_file: str = "psychiatry_emr.log"
    audit_log_retention_days: int = 2555  # 7 years for HIPAA
    
    # DSM-5-TR
    dsm5_criteria_file: str = "config/dsm5_criteria.yaml"
    
    @validator('database_url')
    def validate_database_url(cls, v):
        if not v.startswith(('postgresql://', 'sqlite://')):
            raise ValueError('Database URL must be PostgreSQL or SQLite')
        return v
    
    @validator('session_timeout_minutes')
    def validate_session_timeout(cls, v):
        if v < 5 or v > 240:  # 5 minutes to 4 hours
            raise ValueError('Session timeout must be between 5 and 240 minutes')
        return v
    
    @validator('encryption_salt')
    def validate_encryption_salt(cls, v):
        if not v:
            raise ValueError('ENCRYPTION_SALT must be set - generate with: python -c "import secrets; print(secrets.token_hex(32))"')
        try:
            bytes.fromhex(v)
        except ValueError:
            raise ValueError('ENCRYPTION_SALT must be a valid hex string')
        return v
    
    @validator('secret_key')
    def validate_secret_key(cls, v):
        if not v or len(v) < 32:
            raise ValueError('SECRET_KEY must be at least 32 characters - generate with: python -c "import secrets; print(secrets.token_urlsafe(32))"')
        return v
    
    class Config:
        env_file = ".env"
        case_sensitive = False

# Global settings instance
settings = Settings()

def generate_env_template():
    """Generate .env template with secure defaults"""
    template = f"""# Psychiatry EMR Configuration
# REQUIRED: Generate these values and keep them secret

# Database connection (supports SSL and .pgpass)
DATABASE_URL=postgresql://localhost:5432/psychiatry_emr?sslmode=prefer

# Encryption salt (generate with: python -c "import secrets; print(secrets.token_hex(32))")
ENCRYPTION_SALT={secrets.token_hex(32)}

# JWT secret key (generate with: python -c "import secrets; print(secrets.token_urlsafe(32))")
SECRET_KEY={secrets.token_urlsafe(32)}

# Optional settings
SESSION_TIMEOUT_MINUTES=30
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION_MINUTES=15
DEBUG_MODE=false
LOG_LEVEL=INFO
"""
    return template
```

## Comprehensive Audit Service

```python
# services/audit_service.py
import reflex as rx
from sqlmodel import Field, Session
from datetime import datetime
from typing import Optional, Dict, Any
import logging
import json

logger = logging.getLogger(__name__)

class AuditLog(rx.Model, table=True):
    """Comprehensive audit logging with better structure"""
    id: Optional[int] = Field(default=None, primary_key=True)
    user_id: int = Field(foreign_key="user.id")
    timestamp: datetime = Field(default_factory=datetime.now)
    action: str  # CREATE, READ, UPDATE, DELETE, SEARCH, ACCESS_DENIED, etc.
    table_name: str
    record_id: Optional[int] = Field(default=None)
    old_values: Optional[str] = Field(default=None)  # JSON string
    new_values: Optional[str] = Field(default=None)  # JSON string
    ip_address: Optional[str] = Field(default=None)
    user_agent: Optional[str] = Field(default=None)
    session_id: Optional[str] = Field(default=None)
    success: bool = Field(default=True)
    error_message: Optional[str] = Field(default=None)

class AuditService:
    """Service for comprehensive audit logging"""
    
    def __init__(self, db_session: Session, user_id: int):
        self.db = db_session
        self.user_id = user_id
    
    def log_action(
        self,
        action: str,
        table_name: str,
        record_id: Optional[int] = None,
        old_values: Optional[Dict[str, Any]] = None,
        new_values: Optional[Dict[str, Any]] = None,
        success: bool = True,
        error_message: Optional[str] = None
    ) -> None:
        """Log an action with comprehensive details"""
        try:
            audit_log = AuditLog(
                user_id=self.user_id,
                action=action,
                table_name=table_name,
                record_id=record_id,
                old_values=json.dumps(old_values) if old_values else None,
                new_values=json.dumps(new_values) if new_values else None,
                success=success,
                error_message=error_message
            )
            
            self.db.add(audit_log)
            self.db.commit()
            
            # Log to application logs as well
            log_message = f"AUDIT: {action} on {table_name}"
            if record_id:
                log_message += f" (ID: {record_id})"
            log_message += f" by user {self.user_id}"
            
            if success:
                logger.info(log_message)
            else:
                logger.warning(f"{log_message} - FAILED: {error_message}")
                
        except Exception as e:
            # Audit logging should never break the application
            logger.error(f"Failed to write audit log: {e}")
```

## Security Implementation Notes

### Key Security Features
1. **Master Password Required**: No file-based key storage, password required on startup
2. **Salt Validation**: Environment variable must contain valid hex salt
3. **Comprehensive Audit Trail**: All data access and modifications logged
4. **Encryption Interface**: Clean abstraction for easy testing and updates
5. **Error Handling**: Secure error messages without data leakage

### Security Checklist
- [ ] **Environment Variables**: ENCRYPTION_SALT and SECRET_KEY properly generated
- [ ] **Master Password**: Complex password policy enforced
- [ ] **Database Security**: PostgreSQL configured with SSL/TLS
- [ ] **Audit Logging**: All sensitive operations tracked
- [ ] **Error Handling**: No PHI exposed in error messages
- [ ] **Session Management**: Proper timeout and invalidation

### Production Security Requirements
```bash
# Generate secure environment variables
python -c "import secrets; print('ENCRYPTION_SALT=' + secrets.token_hex(32))"
python -c "import secrets; print('SECRET_KEY=' + secrets.token_urlsafe(32))"

# Ensure database uses SSL
DATABASE_URL=postgresql://user:pass@localhost:5432/db?sslmode=require

# Configure secure session timeout
SESSION_TIMEOUT_MINUTES=30
MAX_LOGIN_ATTEMPTS=5
```