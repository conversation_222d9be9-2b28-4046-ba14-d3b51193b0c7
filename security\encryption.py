"""
Psychiatry EMR - Encryption Service
Multi-layer security architecture with explicit encryption for PHI data.
"""

from cryptography.fernet import <PERSON>rne<PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import os
import secrets
import logging
from typing import Protocol, Union
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)

class CryptoInterface(Protocol):
    """Interface for encryption operations"""
    def encrypt(self, data: str) -> str: ...
    def decrypt(self, encrypted_data: str) -> str: ...

class EncryptionService:
    """Explicit encryption service for PHI data"""
    
    def __init__(self, master_password: str, salt: Union[str, bytes]):
        """Initialize with master password and required salt"""
        # Convert hex string salt to bytes if needed
        if isinstance(salt, str):
            salt = bytes.fromhex(salt)
        self._derive_key(master_password, salt)
    
    def _derive_key(self, password: str, salt: bytes) -> None:
        """Derive encryption key from master password and salt"""
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        self.cipher = Fernet(key)
    
    def encrypt(self, data: str) -> str:
        """Encrypt sensitive data"""
        if not data:
            return ""
        try:
            encrypted = self.cipher.encrypt(data.encode())
            logger.debug(f"Successfully encrypted data of length {len(data)}")
            return base64.urlsafe_b64encode(encrypted).decode()
        except Exception as e:
            logger.error(f"Encryption failed: {e}")
            raise
    
    def decrypt(self, encrypted_data: str) -> str:
        """Decrypt sensitive data"""
        if not encrypted_data:
            return ""
        try:
            decoded = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted = self.cipher.decrypt(decoded)
            logger.debug(f"Successfully decrypted data")
            return decrypted.decode()
        except Exception as e:
            logger.error(f"Decryption failed: {e}")
            raise

def generate_salt() -> bytes:
    """Generate a cryptographically secure salt"""
    return secrets.token_bytes(32)

def initialize_encryption(master_password: str) -> EncryptionService:
    """Initialize encryption service with master password"""
    # Get salt from environment - MUST be set, no default
    salt_hex = os.environ.get('ENCRYPTION_SALT')
    if not salt_hex:
        raise ValueError("ENCRYPTION_SALT environment variable must be set")
    
    try:
        salt = bytes.fromhex(salt_hex)
    except ValueError:
        raise ValueError("ENCRYPTION_SALT must be a valid hex string")
    
    service = EncryptionService(master_password, salt)
    logger.info("Encryption service initialized")
    return service
