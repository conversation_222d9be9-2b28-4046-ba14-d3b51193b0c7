#!/bin/bash
# scripts/check_code_quality.sh - Code quality verification

echo "🔍 Running code quality checks..."

# Create reports directory
mkdir -p reports

# Format code
echo "🎨 Formatting code with Black..."
black .

# Lint code
echo "🔍 Linting with Ruff..."
ruff check . --fix

# Type checking
echo "🏷️ Type checking with My<PERSON>y..."
mypy .

# Security check
echo "🔒 Security check with Bandit..."
bandit -r . -f json -o reports/security_report.json

# Import sorting
echo "📦 Sorting imports..."
isort .

echo "✅ Code quality checks complete!"
