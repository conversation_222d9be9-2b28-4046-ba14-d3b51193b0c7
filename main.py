"""
Psychiatry EMR - Main Application Entry Point
Production-ready application startup with comprehensive initialization.
"""

import reflex as rx
import logging
import os
import getpass
from pathlib import Path
import sys
from datetime import datetime
from sqlmodel import text

from security.encryption import initialize_encryption, generate_salt
from services.database import get_engine, DB_INDEXES, initialize_database as init_db
from config.settings import get_settings, generate_env_template

# Import pages and components for routing
from pages.dashboard import dashboard_page
from pages.patient_search import patient_search_page
from pages.clinical_assessment import clinical_assessment_page
from components.navigation import main_layout, protected_route
from states.auth_state import AuthState

def setup_logging():
    """Configure comprehensive logging"""
    try:
        settings = get_settings()
        logging.basicConfig(
            level=getattr(logging, settings.log_level.upper()),
            format='%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
            handlers=[
                logging.FileHandler(settings.log_file),
                logging.StreamHandler()
            ]
        )

        # Set specific loggers
        logging.getLogger('sqlalchemy.engine').setLevel(
            logging.INFO if settings.debug_mode else logging.WARNING
        )
    except Exception as e:
        # Fallback logging if settings fail
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[logging.StreamHandler()]
        )
        print(f"Warning: Could not load settings for logging: {e}")

def initialize_database():
    """Initialize database schema and indexes"""
    logger = logging.getLogger(__name__)

    try:
        # Create all tables
        engine = get_engine()

        # Import all models to ensure they're registered
        from models.patient import Patient, UserPatientAccess
        from models.clinical import PresentIllness
        from models.audit import AuditLog

        rx.Model.metadata.create_all(engine)
        logger.info("Database tables created/verified")

        # Apply performance indexes (skip for SQLite due to multi-statement limitation)
        settings = get_settings()
        if not settings.database_url.startswith("sqlite"):
            from sqlmodel import text
            with engine.connect() as conn:
                conn.execute(text(DB_INDEXES))
                conn.commit()
            logger.info("Performance indexes applied")
        else:
            logger.info("Skipping performance indexes for SQLite")

    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        raise

def get_master_password() -> str:
    """Get master password for encryption with validation"""
    if os.getenv("MASTER_PASSWORD"):
        print("⚠️  WARNING: Using master password from environment variable")
        print("   This is not recommended for production use.")
        return os.getenv("MASTER_PASSWORD")

    try:
        settings = get_settings()
        print(f"\n🏥 {settings.app_name} v{settings.app_version}")
    except:
        print(f"\n🏥 Psychiatry EMR v1.0.0")

    print("=" * 50)

    while True:
        password = getpass.getpass("🔐 Enter master password for encryption: ")
        if len(password) < 8:
            print("❌ Password must be at least 8 characters long")
            continue

        confirm = getpass.getpass("🔐 Confirm master password: ")
        if password != confirm:
            print("❌ Passwords do not match")
            continue

        return password

def check_environment():
    """Check that all required environment variables are set"""
    logger = logging.getLogger(__name__)

    # Check if .env exists
    if not Path('.env').exists():
        print("❌ .env file not found!")
        print("📝 Creating template .env file...")

        with open('.env', 'w') as f:
            f.write(generate_env_template())

        print("✅ Template .env file created")
        print("🔧 Please review and update the values, then restart the application")
        sys.exit(1)

    try:
        # This will validate all required settings
        settings = get_settings()
        _ = settings.encryption_salt
        _ = settings.secret_key
        logger.info("Environment configuration validated")
    except Exception as e:
        logger.error(f"Environment validation failed: {e}")
        print(f"❌ Environment validation failed: {e}")
        print("🔧 Please check your .env file")
        sys.exit(1)

def initialize_application():
    """Initialize application with comprehensive setup"""
    logger = logging.getLogger(__name__)

    try:
        settings = get_settings()
        logger.info(f"Initializing {settings.app_name} v{settings.app_version}")
    except:
        logger.info("Initializing Psychiatry EMR v1.0.0")

    try:
        # Check environment first
        check_environment()

        # Get master password for encryption
        master_password = get_master_password()

        # Initialize encryption service
        crypto_service = initialize_encryption(master_password)
        logger.info("✅ Encryption service initialized")

        # Initialize database
        initialize_database()
        logger.info("✅ Database initialized")

        # Store crypto service globally for dependency injection
        # In a real implementation, you'd use a proper DI container
        rx.State._crypto_service = crypto_service

        print("🚀 Application initialization complete!")
        print(f"📊 Starting server on http://localhost:3000")

    except KeyboardInterrupt:
        print("\n❌ Initialization cancelled by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Application initialization failed: {e}")
        print(f"❌ Initialization failed: {e}")
        sys.exit(1)

# Setup logging first
setup_logging()
logger = logging.getLogger(__name__)

# Initialize application
initialize_application()

# Create Reflex app with enhanced configuration
try:
    settings = get_settings()

    app = rx.App(
        style={
            "font_family": "Inter, system-ui, sans-serif",
            "background_color": "#f8fafc",
            "color": "#1e293b",
        },
        stylesheets=[
            "https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
        ],
        theme=rx.theme(
            appearance="light",
            has_background=True,
            radius="medium",
            accent_color="blue",
        ),
    )

    # Configure session settings (if available)
    # app.state_manager.timeout = settings.session_timeout_minutes * 60

except Exception as e:
    logger.warning(f"Could not load settings for app configuration: {e}")
    # Fallback app configuration
    app = rx.App(
        style={
            "font_family": "Inter, system-ui, sans-serif",
            "background_color": "#f8fafc",
            "color": "#1e293b",
        },
    )

# Enhanced index page
@rx.page("/")
def index():
    return rx.container(
        rx.vstack(
            rx.heading("🏥 Psychiatry EMR", size="9"),
            rx.text("Secure Patient Management System", size="5", color="gray"),
            rx.divider(),
            rx.text("✅ Application successfully initialized!", size="4", color="green"),
            rx.text("🔐 Encryption service active", size="3"),
            rx.text("🗄️ Database connected", size="3"),
            rx.text("📊 Ready for patient management", size="3"),
            rx.divider(),
            rx.text("Full UI implementation will be added in Phase 6", size="2", color="gray"),
            spacing="4",
            align="center",
            min_height="100vh",
            justify="center",
        ),
        max_width="800px",
        margin="0 auto",
        padding="2rem",
    )

# Health check endpoint for Docker
@rx.page(route="/health", title="Health Check")
def health_check():
    """Health check endpoint for Docker and monitoring"""
    try:
        # Check database connection
        from services.database import get_engine
        engine = get_engine()
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))

        # Check encryption service
        from config.settings import get_settings
        settings = get_settings()
        if not settings.encryption_salt:
            raise ValueError("Encryption not configured")

        return rx.text("Health check: OK - All services running")
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return rx.text(f"Health check: ERROR - {str(e)}")

# Add pages to the app with routing
app.add_page(index, route="/")
app.add_page(health_check, route="/health")

# Protected pages that require authentication
@rx.page(route="/dashboard", title="Dashboard - Psychiatry EMR")
def dashboard():
    return protected_route(main_layout(dashboard_page()))

@rx.page(route="/patients", title="Patient Search - Psychiatry EMR")
def patients():
    return protected_route(main_layout(patient_search_page()))

@rx.page(route="/assessments", title="Clinical Assessment - Psychiatry EMR")
def assessments():
    return protected_route(main_layout(clinical_assessment_page()))

# Login page (public)
@rx.page(route="/login", title="Login - Psychiatry EMR")
def login():
    return login_page()

def login_page() -> rx.Component:
    """Simple login page"""
    return rx.center(
        rx.card(
            rx.vstack(
                rx.heading("Psychiatry EMR", size="8", color="blue.600", mb=6),
                rx.vstack(
                    rx.input(
                        placeholder="Username",
                        value=AuthState.login_username,
                        on_change=lambda value: AuthState.update_login_form("username", value),
                        width="100%"
                    ),
                    rx.input(
                        placeholder="Password",
                        type="password",
                        value=AuthState.login_password,
                        on_change=lambda value: AuthState.update_login_form("password", value),
                        width="100%"
                    ),
                    rx.button(
                        "Login",
                        on_click=AuthState.login,
                        loading=AuthState.is_loading,
                        color_scheme="blue",
                        width="100%",
                        size="3"
                    ),
                    spacing="4",
                    width="100%"
                ),
                # Error/success messages
                rx.cond(
                    AuthState.error_message,
                    rx.card(
                        rx.hstack(
                            rx.icon("x-circle", color="red.500"),
                            rx.text(AuthState.error_message),
                            spacing="2"
                        ),
                        bg="red.50",
                        border="1px solid",
                        border_color="red.200",
                        width="100%",
                        p=3
                    )
                ),
                rx.cond(
                    AuthState.success_message,
                    rx.card(
                        rx.hstack(
                            rx.icon("check-circle", color="green.500"),
                            rx.text(AuthState.success_message),
                            spacing="2"
                        ),
                        bg="green.50",
                        border="1px solid",
                        border_color="green.200",
                        width="100%",
                        p=3
                    )
                ),
                spacing=4,
                width="100%"
            ),
            max_width="400px",
            p=8
        ),
        min_height="100vh",
        bg="gray.50"
    )

if __name__ == "__main__":
    try:
        settings = get_settings()
        logger.info("Starting Psychiatry EMR application...")
        app.run(
            host="127.0.0.1" if settings.debug_mode else "0.0.0.0",
            port=3000,
            debug=settings.debug_mode
        )
    except Exception as e:
        logger.error(f"Failed to start application: {e}")
        print(f"❌ Failed to start application: {e}")
        sys.exit(1)
