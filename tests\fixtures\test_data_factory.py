"""
Psychiatry EMR - Test Data Factory
Comprehensive test data generation for patients, assessments, and clinical scenarios.
"""

import random
from datetime import date, datetime, timedelta
from typing import List, Dict, Any
from faker import Faker
from models.patient import PatientData
from models.clinical import PresentIllnessData

fake = Faker()

class TestDataFactory:
    """Factory for generating realistic test data"""
    
    # Common psychiatric diagnoses with DSM-5 codes
    PSYCHIATRIC_DIAGNOSES = {
        "296.22": "Major Depressive Disorder, Single Episode, Moderate",
        "296.32": "Major Depressive Disorder, Recurrent, Moderate", 
        "300.02": "Generalized Anxiety Disorder",
        "300.23": "Social Anxiety Disorder",
        "309.81": "Posttraumatic Stress Disorder",
        "300.3": "Obsessive-Compulsive Disorder",
        "296.89": "Bipolar II Disorder",
        "296.7": "Bipolar I Disorder, Most Recent Episode Unspecified",
        "301.83": "Borderline Personality Disorder",
        "314.01": "Attention-Deficit/Hyperactivity Disorder, Combined Presentation"
    }
    
    # Common chief complaints
    CHIEF_COMPLAINTS = [
        "Feeling depressed and hopeless for the past month",
        "Experiencing severe anxiety and panic attacks",
        "Having trouble sleeping and concentrating",
        "Mood swings and irritability affecting relationships",
        "Persistent worry about everything",
        "Flashbacks and nightmares after traumatic event",
        "Compulsive behaviors interfering with daily life",
        "Difficulty focusing and staying organized",
        "Feeling empty and having relationship problems",
        "Hearing voices that others cannot hear"
    ]
    
    # Treatment plan templates
    TREATMENT_PLANS = [
        "1. Initiate SSRI therapy (sertraline 50mg daily)\n2. Weekly individual psychotherapy (CBT)\n3. Sleep hygiene education\n4. Follow-up in 2 weeks",
        "1. Start anxiolytic medication (lorazepam 0.5mg PRN)\n2. Cognitive behavioral therapy for anxiety\n3. Relaxation techniques training\n4. Gradual exposure therapy\n5. Follow-up in 1 week",
        "1. Mood stabilizer therapy (lithium carbonate)\n2. Psychoeducation about bipolar disorder\n3. Family therapy sessions\n4. Mood tracking diary\n5. Follow-up in 3 days",
        "1. Antipsychotic medication (risperidone 2mg daily)\n2. Case management services\n3. Social skills training\n4. Family psychoeducation\n5. Weekly follow-up",
        "1. Trauma-focused therapy (EMDR)\n2. SSRI for comorbid depression\n3. Grounding techniques\n4. Support group referral\n5. Follow-up in 2 weeks"
    ]
    
    @classmethod
    def create_patient(cls, **overrides) -> PatientData:
        """Create a realistic patient with optional field overrides"""
        # Generate realistic demographics
        gender = random.choice(["Male", "Female", "Non-binary"])
        first_name = fake.first_name_male() if gender == "Male" else fake.first_name_female()
        if gender == "Non-binary":
            first_name = fake.first_name()
        
        # Age between 18-80
        age = random.randint(18, 80)
        birth_date = date.today() - timedelta(days=age * 365 + random.randint(0, 365))
        
        # Generate contact info
        phone = fake.phone_number()
        email = f"{first_name.lower()}.{fake.last_name().lower()}@{fake.domain_name()}"
        
        # Emergency contact
        emergency_relationships = ["Spouse", "Parent", "Sibling", "Child", "Friend", "Partner"]
        
        defaults = {
            "first_name": first_name,
            "last_name": fake.last_name(),
            "date_of_birth": birth_date,
            "gender": gender,
            "phone_number": phone,
            "email": email,
            "address": f"{fake.street_address()}, {fake.city()}, {fake.state_abbr()} {fake.zipcode()}",
            "emergency_contact_name": fake.name(),
            "emergency_contact_phone": fake.phone_number(),
            "emergency_contact_relationship": random.choice(emergency_relationships)
        }
        
        # Apply overrides
        defaults.update(overrides)
        
        return PatientData(**defaults)
    
    @classmethod
    def create_patients_batch(cls, count: int, **common_overrides) -> List[PatientData]:
        """Create a batch of patients with optional common overrides"""
        return [cls.create_patient(**common_overrides) for _ in range(count)]
    
    @classmethod
    def create_assessment(cls, patient_id: int, **overrides) -> PresentIllnessData:
        """Create a realistic clinical assessment"""
        # Random assessment date within last 6 months
        days_ago = random.randint(0, 180)
        assessment_date = date.today() - timedelta(days=days_ago)
        
        # Select random diagnosis and related content
        primary_code = random.choice(list(cls.PSYCHIATRIC_DIAGNOSES.keys()))
        primary_diagnosis = cls.PSYCHIATRIC_DIAGNOSES[primary_code]
        
        # Generate realistic history
        duration_weeks = random.randint(2, 52)
        severity = random.choice(["mild", "moderate", "severe"])
        
        history_templates = [
            f"Patient reports {severity} symptoms for approximately {duration_weeks} weeks. Symptoms began gradually and have been progressively worsening. No clear precipitating factors identified.",
            f"Onset of symptoms {duration_weeks} weeks ago following significant life stressor. Patient describes {severity} impairment in daily functioning.",
            f"Chronic symptoms with recent exacerbation over past {duration_weeks} weeks. Patient reports {severity} impact on work and relationships.",
            f"First episode of symptoms beginning {duration_weeks} weeks ago. Patient describes {severity} distress and seeks treatment for the first time."
        ]
        
        defaults = {
            "patient_id": patient_id,
            "assessment_date": assessment_date,
            "chief_complaint": random.choice(cls.CHIEF_COMPLAINTS),
            "history_present_illness": random.choice(history_templates),
            "primary_diagnosis": f"{primary_code} - {primary_diagnosis}",
            "secondary_diagnoses": cls._generate_secondary_diagnoses(),
            "treatment_plan": random.choice(cls.TREATMENT_PLANS)
        }
        
        # Apply overrides
        defaults.update(overrides)
        
        return PresentIllnessData(**defaults)
    
    @classmethod
    def create_assessments_batch(cls, patient_id: int, count: int, **common_overrides) -> List[PresentIllnessData]:
        """Create multiple assessments for a patient"""
        assessments = []
        for i in range(count):
            # Space assessments out over time
            days_offset = i * random.randint(7, 30)  # 1-4 weeks apart
            assessment_date = date.today() - timedelta(days=days_offset)
            
            overrides = {"assessment_date": assessment_date}
            overrides.update(common_overrides)
            
            assessments.append(cls.create_assessment(patient_id, **overrides))
        
        return assessments
    
    @classmethod
    def _generate_secondary_diagnoses(cls) -> str:
        """Generate realistic secondary diagnoses"""
        secondary_options = [
            "",  # No secondary diagnosis
            "Z63.0 - Problems in relationship with spouse or partner",
            "Z56.9 - Unspecified problem related to employment",
            "Z60.2 - Problems related to living alone",
            "F41.9 - Anxiety disorder, unspecified",
            "F32.9 - Major depressive disorder, single episode, unspecified",
            "Z87.891 - Personal history of nicotine dependence"
        ]
        
        # 30% chance of having secondary diagnosis
        if random.random() < 0.3:
            return random.choice(secondary_options[1:])
        return ""
    
    @classmethod
    def create_dsm5_responses(cls, disorder_code: str = "296.22") -> Dict[str, bool]:
        """Create realistic DSM-5 criterion responses"""
        if disorder_code.startswith("296.2"):  # Major Depressive Disorder
            return cls._create_mdd_responses()
        elif disorder_code.startswith("300.02"):  # Generalized Anxiety Disorder
            return cls._create_gad_responses()
        elif disorder_code.startswith("309.81"):  # PTSD
            return cls._create_ptsd_responses()
        else:
            # Generic responses
            return {f"criterion_{i}": random.choice([True, False]) for i in range(1, 10)}
    
    @classmethod
    def _create_mdd_responses(cls) -> Dict[str, bool]:
        """Create realistic MDD criterion responses"""
        # Ensure at least 5 criteria are met (DSM-5 requirement)
        responses = {
            "mdd_1": True,   # Depressed mood (required)
            "mdd_2": random.choice([True, False]),  # Anhedonia
            "mdd_3": random.choice([True, False]),  # Weight changes
            "mdd_4": True,   # Sleep disturbance (common)
            "mdd_5": random.choice([True, False]),  # Psychomotor changes
            "mdd_6": True,   # Fatigue (common)
            "mdd_7": random.choice([True, False]),  # Worthlessness
            "mdd_8": True,   # Concentration problems (common)
            "mdd_9": random.choice([True, False])   # Suicidal ideation
        }
        
        # Ensure at least 5 criteria are met
        true_count = sum(responses.values())
        if true_count < 5:
            # Randomly set additional criteria to True
            false_keys = [k for k, v in responses.items() if not v]
            needed = 5 - true_count
            for key in random.sample(false_keys, min(needed, len(false_keys))):
                responses[key] = True
        
        return responses
    
    @classmethod
    def _create_gad_responses(cls) -> Dict[str, bool]:
        """Create realistic GAD criterion responses"""
        return {
            "gad_1": True,   # Excessive worry (required)
            "gad_2": True,   # Difficult to control worry (required)
            "gad_3": random.choice([True, False]),  # Restlessness
            "gad_4": True,   # Easily fatigued (common)
            "gad_5": random.choice([True, False]),  # Difficulty concentrating
            "gad_6": True,   # Irritability (common)
            "gad_7": random.choice([True, False]),  # Muscle tension
            "gad_8": True,   # Sleep disturbance (common)
        }
    
    @classmethod
    def _create_ptsd_responses(cls) -> Dict[str, bool]:
        """Create realistic PTSD criterion responses"""
        return {
            "ptsd_a": True,  # Exposure to trauma (required)
            "ptsd_b1": True, # Intrusive memories (required)
            "ptsd_b2": random.choice([True, False]),  # Nightmares
            "ptsd_b3": random.choice([True, False]),  # Flashbacks
            "ptsd_c1": True, # Avoidance of thoughts (common)
            "ptsd_c2": random.choice([True, False]),  # Avoidance of reminders
            "ptsd_d1": random.choice([True, False]),  # Negative thoughts
            "ptsd_d2": True, # Negative mood (common)
            "ptsd_e1": random.choice([True, False]),  # Hypervigilance
            "ptsd_e2": True  # Sleep disturbance (common)
        }
    
    @classmethod
    def create_test_scenario(cls, scenario_name: str) -> Dict[str, Any]:
        """Create predefined test scenarios"""
        scenarios = {
            "depression_case": {
                "patient": cls.create_patient(
                    first_name="Sarah",
                    last_name="Johnson",
                    date_of_birth=date(1985, 3, 15),
                    gender="Female"
                ),
                "assessment": cls.create_assessment(
                    patient_id=1,  # Will be updated
                    chief_complaint="Feeling depressed and hopeless for the past 6 weeks",
                    primary_diagnosis="296.22 - Major Depressive Disorder, Single Episode, Moderate"
                ),
                "dsm5_responses": cls._create_mdd_responses()
            },
            
            "anxiety_case": {
                "patient": cls.create_patient(
                    first_name="Michael",
                    last_name="Chen",
                    date_of_birth=date(1992, 8, 22),
                    gender="Male"
                ),
                "assessment": cls.create_assessment(
                    patient_id=1,  # Will be updated
                    chief_complaint="Constant worry and panic attacks",
                    primary_diagnosis="300.02 - Generalized Anxiety Disorder"
                ),
                "dsm5_responses": cls._create_gad_responses()
            },
            
            "complex_case": {
                "patient": cls.create_patient(
                    first_name="Alex",
                    last_name="Rivera",
                    date_of_birth=date(1978, 11, 5),
                    gender="Non-binary"
                ),
                "assessment": cls.create_assessment(
                    patient_id=1,  # Will be updated
                    chief_complaint="Multiple symptoms affecting work and relationships",
                    primary_diagnosis="296.89 - Bipolar II Disorder",
                    secondary_diagnoses="300.02 - Generalized Anxiety Disorder"
                ),
                "dsm5_responses": {
                    "bipolar_1": True,
                    "bipolar_2": True,
                    "bipolar_3": False,
                    "gad_1": True,
                    "gad_2": True
                }
            }
        }
        
        return scenarios.get(scenario_name, {})
    
    @classmethod
    def create_bulk_test_data(cls, num_patients: int = 100) -> Dict[str, List]:
        """Create bulk test data for performance testing"""
        patients = cls.create_patients_batch(num_patients)
        
        # Create 1-3 assessments per patient
        assessments = []
        dsm5_responses = []
        
        for i, patient in enumerate(patients):
            patient_id = i + 1  # Simulated patient ID
            num_assessments = random.randint(1, 3)
            
            patient_assessments = cls.create_assessments_batch(
                patient_id, 
                num_assessments
            )
            assessments.extend(patient_assessments)
            
            # Create DSM-5 responses for each assessment
            for assessment in patient_assessments:
                responses = cls.create_dsm5_responses(
                    assessment.primary_diagnosis.split(" - ")[0]
                )
                dsm5_responses.append({
                    "patient_id": patient_id,
                    "assessment_id": len(dsm5_responses) + 1,
                    "responses": responses
                })
        
        return {
            "patients": patients,
            "assessments": assessments,
            "dsm5_responses": dsm5_responses
        }
