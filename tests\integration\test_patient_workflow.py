# tests/integration/test_patient_workflow.py
import pytest
from datetime import date
from models.patient import PatientData
from models.clinical import PresentIllnessData
from services.patient_service import PatientService
from services.clinical_service import ClinicalService
from services.audit_service import AuditService
from sqlmodel import select
from models.audit import AuditLog

def test_complete_patient_workflow(test_db, test_crypto_service):
    """Test complete patient creation and retrieval workflow"""
    service = PatientService(test_db, user_id=1, crypto_service=test_crypto_service)
    
    # Create patient
    patient_data = PatientData(
        name="<PERSON>",
        dob=date(1990, 1, 1),
        phone="**********",
        email="<EMAIL>"
    )
    
    created = service.create_patient(patient_data)
    assert created.id is not None
    assert created.name == "<PERSON>"
    assert created.phone == "(*************"  # Should be formatted
    
    # Retrieve patient
    retrieved = service.get_patient(created.id)
    assert retrieved is not None
    assert retrieved.name == created.name
    
    # Search for patient
    search_results = service.search_patients("<PERSON>")
    assert len(search_results["patients"]) >= 1
    assert any(p.id == created.id for p in search_results["patients"])

def test_patient_data_validation(test_db, test_crypto_service):
    """Test patient data validation"""
    service = PatientService(test_db, user_id=1, crypto_service=test_crypto_service)
    
    # Test invalid phone number
    with pytest.raises(ValueError):
        PatientData(
            name="John Doe",
            dob=date(1990, 1, 1),
            phone="invalid_phone"
        )
    
    # Test empty name
    with pytest.raises(ValueError):
        PatientData(
            name="",
            dob=date(1990, 1, 1)
        )
    
    # Test valid data
    valid_data = PatientData(
        name="Jane Smith",
        dob=date(1985, 5, 15),
        phone="**********"
    )
    assert valid_data.name == "Jane Smith"
    assert valid_data.phone == "(*************"

def test_duplicate_detection(test_db, test_crypto_service):
    """Test duplicate patient detection"""
    service = PatientService(test_db, user_id=1, crypto_service=test_crypto_service)
    
    # Create original patient
    original = service.create_patient(PatientData(name="Jane Smith", dob=date(1985, 5, 15)))
    
    # Check for duplicates with similar data
    similar_data = PatientData(name="Jane Smith", dob=date(1985, 5, 15))
    duplicates = service.find_potential_duplicates(similar_data)
    
    assert len(duplicates) >= 1
    assert duplicates[0]["id"] == original.id
    assert duplicates[0]["similarity_score"] >= 85

def test_patient_search_pagination(test_db, test_crypto_service):
    """Test patient search with pagination"""
    service = PatientService(test_db, user_id=1, crypto_service=test_crypto_service)
    
    # Create multiple test patients
    for i in range(30):
        service.create_patient(PatientData(
            name=f"Test Patient {i:02d}",
            dob=date(1990, 1, 1)
        ))
    
    # Test first page
    results_page1 = service.search_patients("Test Patient", page=1, page_size=10)
    assert len(results_page1["patients"]) == 10
    assert results_page1["page"] == 1
    assert results_page1["total_pages"] >= 3
    
    # Test second page
    results_page2 = service.search_patients("Test Patient", page=2, page_size=10)
    assert len(results_page2["patients"]) == 10
    assert results_page2["page"] == 2
    
    # Ensure different results on different pages
    page1_ids = {p.id for p in results_page1["patients"]}
    page2_ids = {p.id for p in results_page2["patients"]}
    assert page1_ids.isdisjoint(page2_ids)  # No overlap

def test_patient_encryption_integrity(test_db, test_crypto_service):
    """Test that patient data encryption maintains integrity"""
    service = PatientService(test_db, user_id=1, crypto_service=test_crypto_service)
    
    # Create patient with special characters
    original_data = PatientData(
        name="José María García-López",
        dob=date(1975, 12, 25),
        phone="**********",
        email="<EMAIL>",
        address="123 Calle Principal, Ciudad, País"
    )
    
    created = service.create_patient(original_data)
    retrieved = service.get_patient(created.id)
    
    # Verify all data is preserved correctly
    assert retrieved.name == original_data.name
    assert retrieved.dob == original_data.dob
    assert retrieved.phone == "(*************"  # Formatted
    assert retrieved.email == original_data.email
    assert retrieved.address == original_data.address

def test_patient_soft_delete(test_db, test_crypto_service):
    """Test patient soft delete functionality"""
    service = PatientService(test_db, user_id=1, crypto_service=test_crypto_service)
    
    # Create patient
    patient = service.create_patient(PatientData(name="Test Patient", dob=date(1990, 1, 1)))
    
    # Verify patient exists
    retrieved = service.get_patient(patient.id)
    assert retrieved is not None
    
    # Note: Soft delete functionality would be implemented in the service
    # For now, we test that inactive patients are not returned in searches
    
    # This would be the soft delete implementation:
    # service.deactivate_patient(patient.id)
    # retrieved_after_delete = service.get_patient(patient.id)
    # assert retrieved_after_delete is None

def test_patient_merge_workflow(test_db, test_crypto_service):
    """Test patient merge workflow for duplicates"""
    service = PatientService(test_db, user_id=1, crypto_service=test_crypto_service)
    
    # Create two similar patients (duplicates)
    patient1 = service.create_patient(PatientData(
        name="John Smith",
        dob=date(1980, 6, 15),
        phone="**********"
    ))
    
    patient2 = service.create_patient(PatientData(
        name="John Smith",
        dob=date(1980, 6, 15),
        phone="**********"
    ))
    
    # Verify both exist
    assert service.get_patient(patient1.id) is not None
    assert service.get_patient(patient2.id) is not None
    
    # Note: Merge functionality would be implemented in the service
    # This test demonstrates the expected workflow
    
    # Find duplicates
    duplicates = service.find_potential_duplicates(PatientData(
        name="John Smith",
        dob=date(1980, 6, 15)
    ))

    assert len(duplicates) >= 2

@pytest.mark.integration
def test_complete_patient_lifecycle_with_assessments(test_db, test_user, test_crypto_service):
    """Test complete patient lifecycle from creation to clinical assessment"""
    # Initialize services
    audit_service = AuditService(test_db, test_user.id)
    patient_service = PatientService(test_db, audit_service, test_user.id, test_crypto_service)
    clinical_service = ClinicalService(test_db, audit_service, test_user.id)

    # Step 1: Create patient
    patient_data = PatientData(
        first_name="John",
        last_name="Doe",
        date_of_birth=date(1985, 6, 15),
        gender="Male",
        phone_number="************",
        email="<EMAIL>"
    )

    patient = patient_service.create_patient(patient_data)
    assert patient is not None
    assert patient.id is not None

    # Step 2: Create clinical assessment
    assessment_data = PresentIllnessData(
        patient_id=patient.id,
        assessment_date=date.today(),
        chief_complaint="Feeling depressed and anxious",
        history_present_illness="Patient reports onset of symptoms 3 weeks ago",
        primary_diagnosis="296.22",
        treatment_plan="Initiate therapy and medication"
    )

    assessment = clinical_service.create_assessment(assessment_data)
    assert assessment is not None
    assert assessment.patient_id == patient.id

    # Step 3: Verify audit trail
    audit_logs = test_db.exec(select(AuditLog)).all()
    assert len(audit_logs) >= 2  # Patient creation + assessment creation

    # Verify patient creation audit
    patient_create_log = next((log for log in audit_logs if log.action == "CREATE" and log.table_name == "patient"), None)
    assert patient_create_log is not None
    assert patient_create_log.user_id == test_user.id

@pytest.mark.integration
def test_multi_user_data_isolation(test_db, test_crypto_service):
    """Test data isolation between different users"""
    # Create services for two users
    audit_service_1 = AuditService(test_db, 1)
    audit_service_2 = AuditService(test_db, 2)

    patient_service_1 = PatientService(test_db, audit_service_1, 1, test_crypto_service)
    patient_service_2 = PatientService(test_db, audit_service_2, 2, test_crypto_service)

    # User 1 creates patient
    patient_data = PatientData(
        first_name="Alice",
        last_name="Johnson",
        date_of_birth=date(1988, 3, 22),
        phone_number="************"
    )

    patient = patient_service_1.create_patient(patient_data)
    assert patient is not None

    # User 1 can access their patient
    retrieved_by_user1 = patient_service_1.get_patient(patient.id)
    assert retrieved_by_user1 is not None

    # User 2 cannot access user 1's patient
    retrieved_by_user2 = patient_service_2.get_patient(patient.id)
    assert retrieved_by_user2 is None
    duplicate_ids = {d["id"] for d in duplicates}
    assert patient1.id in duplicate_ids
    assert patient2.id in duplicate_ids
