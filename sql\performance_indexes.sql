-- Psychiatry EMR - Performance Indexes
-- Performance-optimized indexes for the Psychiatry EMR

-- Patient table indexes
CREATE INDEX IF NOT EXISTS idx_patient_dob ON patient(dob);
CREATE INDEX IF NOT EXISTS idx_patient_created_at ON patient(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_patient_active ON patient(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_patient_merged_into ON patient(merged_into) WHERE merged_into IS NOT NULL;

-- Composite index for name + DOB uniqueness checking (on encrypted data)
CREATE INDEX IF NOT EXISTS idx_patient_name_dob ON patient(name_encrypted, dob) WHERE is_active = true;

-- Audit log indexes for performance monitoring
CREATE INDEX IF NOT EXISTS idx_audit_user_ts ON audit_log(user_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_audit_table_record ON audit_log(table_name, record_id);
CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_audit_failed ON audit_log(success, timestamp DESC) WHERE success = false;

-- User access indexes
CREATE INDEX IF NOT EXISTS idx_user_access_patient ON user_patient_access(patient_id) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_user_access_user ON user_patient_access(user_id) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_user_access_granted ON user_patient_access(granted_at DESC);

-- Clinical data indexes
CREATE INDEX IF NOT EXISTS idx_present_illness_patient ON present_illness(patient_id);
CREATE INDEX IF NOT EXISTS idx_present_illness_date ON present_illness(assessment_date DESC);
CREATE INDEX IF NOT EXISTS idx_present_illness_diagnosis ON present_illness(primary_diagnosis);

-- Partial indexes for active records only (more efficient)
CREATE INDEX IF NOT EXISTS idx_patient_active_only ON patient(id, created_at) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_access_active_only ON user_patient_access(user_id, patient_id) WHERE is_active = true;

-- Text search indexes (requires pg_trgm extension)
-- Enable trigram search for encrypted fields when possible
CREATE INDEX IF NOT EXISTS idx_patient_name_trgm ON patient USING gin(name_encrypted gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_patient_phone_trgm ON patient USING gin(phone_encrypted gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_patient_email_trgm ON patient USING gin(email_encrypted gin_trgm_ops);

-- Clinical content text search indexes
CREATE INDEX IF NOT EXISTS idx_present_illness_chief_complaint_trgm ON present_illness
    USING gin(chief_complaint gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_present_illness_history_trgm ON present_illness
    USING gin(history_present_illness gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_present_illness_treatment_plan_trgm ON present_illness
    USING gin(treatment_plan gin_trgm_ops);

-- DSM-5 criteria evaluation indexes
CREATE INDEX IF NOT EXISTS idx_present_illness_dsm5_criteria ON present_illness
    USING gin(dsm5_criteria_met);

-- Security monitoring indexes for bulk access detection
CREATE INDEX IF NOT EXISTS idx_audit_log_bulk_access ON audit_log(user_id, timestamp)
    WHERE table_name = 'patient' AND action = 'READ';
CREATE INDEX IF NOT EXISTS idx_audit_log_patient_access ON audit_log(user_id, record_id, timestamp)
    WHERE table_name = 'patient' AND action IN ('READ', 'UPDATE', 'DELETE');
CREATE INDEX IF NOT EXISTS idx_audit_log_authentication ON audit_log(user_id, timestamp)
    WHERE table_name = 'authentication';
CREATE INDEX IF NOT EXISTS idx_audit_log_dsm5_evaluations ON audit_log(timestamp, new_values)
    WHERE action = 'DSM5_EVAL';

-- Performance monitoring functions
CREATE OR REPLACE FUNCTION refresh_table_statistics()
RETURNS void AS $$
BEGIN
    ANALYZE patient;
    ANALYZE user_patient_access;
    ANALYZE present_illness;
    ANALYZE audit_log;

    RAISE NOTICE 'Table statistics refreshed at %', NOW();
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION get_index_usage_stats()
RETURNS TABLE(
    schemaname text,
    tablename text,
    indexname text,
    idx_scan bigint,
    idx_tup_read bigint,
    idx_tup_fetch bigint
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        s.schemaname::text,
        s.tablename::text,
        s.indexrelname::text,
        s.idx_scan,
        s.idx_tup_read,
        s.idx_tup_fetch
    FROM pg_stat_user_indexes s
    WHERE s.schemaname = 'public'
    ORDER BY s.idx_scan DESC;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION get_unused_indexes()
RETURNS TABLE(
    schemaname text,
    tablename text,
    indexname text,
    index_size text
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        s.schemaname::text,
        s.tablename::text,
        s.indexrelname::text,
        pg_size_pretty(pg_relation_size(s.indexrelid))::text as index_size
    FROM pg_stat_user_indexes s
    JOIN pg_index i ON s.indexrelid = i.indexrelid
    WHERE s.idx_scan = 0
    AND s.schemaname = 'public'
    AND NOT i.indisunique
    ORDER BY pg_relation_size(s.indexrelid) DESC;
END;
$$ LANGUAGE plpgsql;

-- Update table statistics
ANALYZE patient;
ANALYZE user_patient_access;
ANALYZE present_illness;
ANALYZE audit_log;

-- Enhanced comments
COMMENT ON INDEX idx_patient_dob IS 'Index for age-based queries and statistics';
COMMENT ON INDEX idx_audit_user_ts IS 'Primary audit query index for user activity reports';
COMMENT ON INDEX idx_patient_active_only IS 'Partial index covering most patient queries';
COMMENT ON INDEX idx_patient_name_trgm IS 'Trigram index for fuzzy patient name search';
COMMENT ON INDEX idx_present_illness_dsm5_criteria IS 'GIN index for DSM-5 criteria JSON queries';
COMMENT ON INDEX idx_audit_log_bulk_access IS 'Security monitoring index for bulk access detection';
COMMENT ON FUNCTION refresh_table_statistics() IS 'Refresh table statistics for optimal query planning';
COMMENT ON FUNCTION get_index_usage_stats() IS 'Monitor index usage statistics for performance tuning';
COMMENT ON FUNCTION get_unused_indexes() IS 'Identify unused indexes that may be candidates for removal';
