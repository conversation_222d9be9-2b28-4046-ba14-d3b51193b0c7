"""
Psychiatry EMR - Audit Trail Security Tests
Comprehensive tests for audit logging and security monitoring.
"""

import pytest
from datetime import datetime, timedelta
from sqlmodel import select
from models.audit import AuditLog
from services.audit_service import AuditService

@pytest.mark.security
class TestAuditTrail:
    """Test audit trail functionality"""
    
    def test_audit_log_creation(self, test_db, test_user):
        """Test basic audit log creation"""
        audit_service = AuditService(test_db, test_user.id)
        
        audit_service.log_action(
            action="CREATE",
            table_name="patient",
            record_id=123,
            new_values={"name": "Test Patient"},
            success=True
        )
        
        # Verify audit log was created
        audit_logs = test_db.exec(select(AuditLog)).all()
        assert len(audit_logs) == 1
        
        log = audit_logs[0]
        assert log.user_id == test_user.id
        assert log.action == "CREATE"
        assert log.table_name == "patient"
        assert log.record_id == 123
        assert log.success is True
        assert log.timestamp is not None
    
    def test_audit_log_with_old_and_new_values(self, test_db, test_user):
        """Test audit log with both old and new values"""
        audit_service = AuditService(test_db, test_user.id)
        
        old_values = {"name": "Old Name", "phone": "555-1234"}
        new_values = {"name": "New Name", "phone": "555-5678"}
        
        audit_service.log_action(
            action="UPDATE",
            table_name="patient",
            record_id=456,
            old_values=old_values,
            new_values=new_values,
            success=True
        )
        
        log = test_db.exec(select(AuditLog)).first()
        assert log.old_values is not None
        assert log.new_values is not None
        assert "Old Name" in log.old_values
        assert "New Name" in log.new_values
    
    def test_audit_log_failure_tracking(self, test_db, test_user):
        """Test audit log tracks failures"""
        audit_service = AuditService(test_db, test_user.id)
        
        audit_service.log_action(
            action="DELETE",
            table_name="patient",
            record_id=789,
            success=False,
            error_message="Access denied"
        )
        
        log = test_db.exec(select(AuditLog)).first()
        assert log.success is False
        assert log.error_message == "Access denied"
    
    def test_patient_access_logging(self, test_db, test_user):
        """Test patient access convenience method"""
        audit_service = AuditService(test_db, test_user.id)
        
        audit_service.log_patient_access(
            patient_id=123,
            action="READ",
            success=True
        )
        
        log = test_db.exec(select(AuditLog)).first()
        assert log.action == "READ"
        assert log.table_name == "patient"
        assert log.record_id == 123
    
    def test_search_logging(self, test_db, test_user):
        """Test search operation logging"""
        audit_service = AuditService(test_db, test_user.id)
        
        audit_service.log_search(
            search_term="John Doe",
            results_count=5,
            table_name="patient"
        )
        
        log = test_db.exec(select(AuditLog)).first()
        assert log.action == "SEARCH"
        assert log.table_name == "patient"
        assert "John Doe" in log.new_values
        assert "5" in log.new_values
    
    def test_authentication_logging(self, test_db, test_user):
        """Test authentication event logging"""
        audit_service = AuditService(test_db, test_user.id)
        
        # Successful login
        audit_service.log_authentication("LOGIN_SUCCESS", success=True)
        
        # Failed login
        audit_service.log_authentication(
            "LOGIN_FAILED", 
            success=False, 
            error_message="Invalid password"
        )
        
        logs = test_db.exec(select(AuditLog)).all()
        assert len(logs) == 2
        
        success_log = next(log for log in logs if log.success)
        failed_log = next(log for log in logs if not log.success)
        
        assert success_log.action == "LOGIN_SUCCESS"
        assert failed_log.action == "LOGIN_FAILED"
        assert failed_log.error_message == "Invalid password"
    
    def test_dsm5_evaluation_logging(self, test_db, test_user):
        """Test DSM-5 evaluation logging"""
        audit_service = AuditService(test_db, test_user.id)
        
        audit_service.log_dsm5_evaluation(
            disorder_code="296.22",
            criteria_met=True,
            confidence_level=0.85
        )
        
        log = test_db.exec(select(AuditLog)).first()
        assert log.action == "DSM5_EVAL"
        assert log.table_name == "dsm5_criteria"
        assert "296.22" in log.new_values
        assert "true" in log.new_values.lower()
        assert "0.85" in log.new_values
    
    def test_patient_merge_logging(self, test_db, test_user):
        """Test patient merge operation logging"""
        audit_service = AuditService(test_db, test_user.id)
        
        audit_service.log_patient_merge(
            primary_id=123,
            duplicate_id=456,
            success=True
        )
        
        log = test_db.exec(select(AuditLog)).first()
        assert log.action == "MERGE"
        assert log.table_name == "patient"
        assert log.record_id == 456  # Duplicate ID
        assert "123" in log.new_values  # Primary ID
    
    def test_encryption_failure_logging(self, test_db, test_user):
        """Test encryption failure logging"""
        audit_service = AuditService(test_db, test_user.id)
        
        audit_service.log_encryption_failure(
            table_name="patient",
            record_id=123,
            error_message="Decryption failed: invalid token"
        )
        
        log = test_db.exec(select(AuditLog)).first()
        assert log.action == "DECRYPT_FAILED"
        assert log.table_name == "patient"
        assert log.record_id == 123
        assert log.success is False
        assert "invalid token" in log.error_message
    
    def test_audit_service_handles_exceptions_gracefully(self, test_db, test_user):
        """Test audit service doesn't break application on errors"""
        audit_service = AuditService(test_db, test_user.id)
        
        # Close the database session to simulate error
        test_db.close()
        
        # This should not raise an exception
        audit_service.log_action(
            action="TEST",
            table_name="test_table",
            success=True
        )
        
        # Application should continue working

@pytest.mark.security
class TestAuditSecurity:
    """Test audit trail security properties"""
    
    def test_audit_logs_are_immutable(self, test_db, test_user):
        """Test that audit logs cannot be easily modified"""
        audit_service = AuditService(test_db, test_user.id)
        
        audit_service.log_action(
            action="CREATE",
            table_name="patient",
            record_id=123,
            success=True
        )
        
        log = test_db.exec(select(AuditLog)).first()
        original_timestamp = log.timestamp
        original_action = log.action
        
        # Attempt to modify (this would be prevented by proper DB constraints in production)
        log.action = "MODIFIED"
        log.timestamp = datetime.utcnow()
        
        # In a real system, this should fail due to DB constraints
        # For this test, we verify the original values are preserved in our logic
        assert original_action == "CREATE"
        assert original_timestamp is not None
    
    def test_audit_logs_include_user_attribution(self, test_db, test_user):
        """Test all audit logs include user attribution"""
        audit_service = AuditService(test_db, test_user.id)
        
        # Test various log types
        audit_service.log_action("CREATE", "patient", success=True)
        audit_service.log_patient_access(123)
        audit_service.log_search("test", 5)
        audit_service.log_authentication("LOGIN")
        
        logs = test_db.exec(select(AuditLog)).all()
        
        # All logs should have user attribution
        for log in logs:
            assert log.user_id == test_user.id
            assert log.user_id is not None
    
    def test_audit_logs_have_precise_timestamps(self, test_db, test_user):
        """Test audit logs have precise timestamps"""
        audit_service = AuditService(test_db, test_user.id)
        
        before_time = datetime.utcnow()
        
        audit_service.log_action("TEST", "test_table", success=True)
        
        after_time = datetime.utcnow()
        
        log = test_db.exec(select(AuditLog)).first()
        
        assert before_time <= log.timestamp <= after_time
        assert log.timestamp is not None
    
    def test_sensitive_data_not_logged_in_plain_text(self, test_db, test_user):
        """Test sensitive data is not logged in plain text"""
        audit_service = AuditService(test_db, test_user.id)
        
        # Simulate logging with sensitive data
        sensitive_data = {
            "ssn": "***********",
            "credit_card": "4111-1111-1111-1111",
            "password": "secret_password"
        }
        
        audit_service.log_action(
            action="UPDATE",
            table_name="patient",
            new_values=sensitive_data,
            success=True
        )
        
        log = test_db.exec(select(AuditLog)).first()
        
        # In a production system, sensitive fields should be masked or encrypted
        # This test documents the current behavior and can be enhanced
        assert log.new_values is not None
        # Note: In production, implement field-level masking for sensitive data
    
    def test_bulk_access_detection(self, test_db, test_user):
        """Test detection of bulk access patterns"""
        audit_service = AuditService(test_db, test_user.id)
        
        # Simulate bulk patient access
        for i in range(50):
            audit_service.log_patient_access(i, action="READ")
        
        # Query for bulk access pattern
        recent_time = datetime.utcnow() - timedelta(minutes=5)
        bulk_access_logs = test_db.exec(
            select(AuditLog).where(
                AuditLog.user_id == test_user.id,
                AuditLog.action == "read",
                AuditLog.table_name == "patient",
                AuditLog.timestamp >= recent_time
            )
        ).all()
        
        assert len(bulk_access_logs) == 50
        
        # In production, this would trigger security alerts
        if len(bulk_access_logs) > 20:  # Threshold for bulk access
            # Security alert would be triggered here
            assert True  # Placeholder for alert logic
    
    def test_failed_access_tracking(self, test_db, test_user):
        """Test tracking of failed access attempts"""
        audit_service = AuditService(test_db, test_user.id)
        
        # Simulate multiple failed access attempts
        for i in range(5):
            audit_service.log_patient_access(
                123, 
                action="read", 
                success=False, 
                error_message="Access denied"
            )
        
        failed_attempts = test_db.exec(
            select(AuditLog).where(
                AuditLog.user_id == test_user.id,
                AuditLog.success == False,
                AuditLog.table_name == "patient"
            )
        ).all()
        
        assert len(failed_attempts) == 5
        
        # In production, multiple failed attempts would trigger security review
        if len(failed_attempts) >= 3:
            # Security review would be triggered here
            assert True  # Placeholder for security review logic
