"""
Psychiatry EMR - Application Settings
Configuration management with Pydantic validation.
"""

from pydantic import BaseModel, field_validator
from typing import Optional
import os
import secrets
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Settings(BaseModel):
    """Application settings with validation"""
    
    # Database - support full PostgreSQL connection strings
    database_url: str = os.getenv("DATABASE_URL", "postgresql://localhost:5432/psychiatry_emr")
    db_pool_size: int = int(os.getenv("DB_POOL_SIZE", "5"))
    db_max_overflow: int = int(os.getenv("DB_MAX_OVERFLOW", "10"))

    # Security - REQUIRED settings, no defaults
    encryption_salt: str = os.getenv("ENCRYPTION_SALT", "")  # MUST be set in .env
    secret_key: str = os.getenv("SECRET_KEY", "")  # JWT signing key for Reflex auth
    session_timeout_minutes: int = int(os.getenv("SESSION_TIMEOUT_MINUTES", "30"))
    max_login_attempts: int = int(os.getenv("MAX_LOGIN_ATTEMPTS", "5"))
    lockout_duration_minutes: int = int(os.getenv("LOCKOUT_DURATION_MINUTES", "15"))
    
    # Application
    app_name: str = os.getenv("APP_NAME", "Psychiatry EMR")
    app_version: str = os.getenv("APP_VERSION", "1.0.0")
    debug_mode: bool = os.getenv("DEBUG_MODE", "false").lower() == "true"

    # Logging
    log_level: str = os.getenv("LOG_LEVEL", "INFO")
    log_file: str = os.getenv("LOG_FILE", "psychiatry_emr.log")
    audit_log_retention_days: int = int(os.getenv("AUDIT_LOG_RETENTION_DAYS", "2555"))  # 7 years for HIPAA

    # DSM-5-TR
    dsm5_criteria_file: str = os.getenv("DSM5_CRITERIA_FILE", "config/dsm5_criteria.yaml")
    
    @field_validator('database_url')
    @classmethod
    def validate_database_url(cls, v):
        if not v.startswith(('postgresql://', 'sqlite://')):
            raise ValueError('Database URL must be PostgreSQL or SQLite')
        return v

    @field_validator('session_timeout_minutes')
    @classmethod
    def validate_session_timeout(cls, v):
        if v < 5 or v > 240:  # 5 minutes to 4 hours
            raise ValueError('Session timeout must be between 5 and 240 minutes')
        return v

    @field_validator('encryption_salt')
    @classmethod
    def validate_encryption_salt(cls, v):
        if not v:
            raise ValueError('ENCRYPTION_SALT must be set - generate with: python -c "import secrets; print(secrets.token_hex(32))"')
        try:
            bytes.fromhex(v)
        except ValueError:
            raise ValueError('ENCRYPTION_SALT must be a valid hex string')
        return v

    @field_validator('secret_key')
    @classmethod
    def validate_secret_key(cls, v):
        if not v or len(v) < 32:
            raise ValueError('SECRET_KEY must be at least 32 characters - generate with: python -c "import secrets; print(secrets.token_urlsafe(32))"')
        return v
    
    model_config = {"env_file": ".env", "case_sensitive": False}

def generate_env_template():
    """Generate .env template with secure defaults"""
    template = f"""# Psychiatry EMR Configuration
# REQUIRED: Generate these values and keep them secret

# Database connection (supports SSL and .pgpass)
DATABASE_URL=postgresql://localhost:5432/psychiatry_emr?sslmode=prefer

# Encryption salt (generate with: python -c "import secrets; print(secrets.token_hex(32))")
ENCRYPTION_SALT={secrets.token_hex(32)}

# JWT secret key (generate with: python -c "import secrets; print(secrets.token_urlsafe(32))")
SECRET_KEY={secrets.token_urlsafe(32)}

# Optional settings
SESSION_TIMEOUT_MINUTES=30
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION_MINUTES=15
DEBUG_MODE=false
LOG_LEVEL=INFO
"""
    return template

# Global settings instance - will be created when needed
settings = None

def get_settings() -> Settings:
    """Get settings instance with lazy loading"""
    global settings
    if settings is None:
        settings = Settings()
    return settings
