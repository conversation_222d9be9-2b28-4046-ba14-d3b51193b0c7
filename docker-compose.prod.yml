# docker-compose.prod.yml - Production configuration

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD:-password}@db:5432/${POSTGRES_DB:-psychiatry_emr}
      - DEBUG_MODE=false
      - ENCRYPTION_SALT=${ENCRYPTION_SALT}
      - SECRET_KEY=${SECRET_KEY}
      - LOG_LEVEL=INFO
      - POSTGRES_DB=${POSTGRES_DB:-psychiatry_emr}
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-password}
      - REDIS_PASSWORD=${REDIS_PASSWORD:-redis_password}
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_started
    volumes:
      - ./logs:/app/logs
      - /etc/ssl/certs:/etc/ssl/certs:ro
    networks:
      - emr_network
    restart: always
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:3000/health" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-psychiatry_emr}
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-password}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./sql:/docker-entrypoint-initdb.d
      - ./backups:/backups
    networks:
      - emr_network
    restart: always
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres} -d ${POSTGRES_DB:-psychiatry_emr}" ]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    command: >
      postgres -c shared_preload_libraries=pg_stat_statements -c log_statement=all -c log_min_duration_statement=1000 -c max_connections=100 -c shared_buffers=256MB

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    networks:
      - emr_network
    restart: always
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis_password}
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.25'
    healthcheck:
      test: [ "CMD", "redis-cli", "--raw", "incr", "ping" ]
      interval: 10s
      timeout: 3s
      retries: 5

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - /etc/ssl/certs:/etc/ssl/certs:ro
      - /etc/ssl/private:/etc/ssl/private:ro
    depends_on:
      - app
    networks:
      - emr_network
    restart: always

volumes:
  postgres_data:
  redis_data:


networks:
  emr_network:
    driver: bridge
