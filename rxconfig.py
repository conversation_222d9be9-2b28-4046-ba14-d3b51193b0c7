"""
Psychiatry EMR - Reflex Configuration
Application configuration for Reflex framework.
"""

import reflex as rx
from config.settings import get_settings

# Get application settings
try:
    settings = get_settings()
except Exception:
    # Fallback configuration if settings can't be loaded
    class FallbackSettings:
        app_name = "Psychiatry EMR"
        debug_mode = False
        
    settings = FallbackSettings()

class PsychiatryEMRConfig(rx.Config):
    """Psychiatry EMR Reflex configuration"""
    
    app_name = "psychiatry_emr"
    
    # Disable sitemap plugin warnings
    disable_plugins = ["reflex.plugins.sitemap.SitemapPlugin"]
    
    # Frontend configuration
    frontend_packages = [
        "react-icons",
        "react-hook-form",
        "@chakra-ui/react",
        "@emotion/react",
        "@emotion/styled",
        "framer-motion"
    ]
    
    # Backend configuration
    backend_port = 8000
    frontend_port = 3000
    
    # Database configuration
    db_url = getattr(settings, 'database_url', 'sqlite:///psychiatry_emr.db')
    
    # Environment-specific settings
    env = "dev" if getattr(settings, 'debug_mode', False) else "prod"
    
    # API configuration
    api_url = "http://localhost:8000" if getattr(settings, 'debug_mode', False) else "https://api.psychiatry-emr.com"
    
    # Tailwind configuration
    tailwind = {
        "theme": {
            "extend": {
                "colors": {
                    "primary": {
                        "50": "#eff6ff",
                        "500": "#3b82f6",
                        "600": "#2563eb",
                        "700": "#1d4ed8",
                        "900": "#1e3a8a"
                    },
                    "medical": {
                        "50": "#f0f9ff",
                        "100": "#e0f2fe",
                        "500": "#0ea5e9",
                        "600": "#0284c7",
                        "700": "#0369a1"
                    }
                },
                "fontFamily": {
                    "sans": ["Inter", "system-ui", "sans-serif"],
                    "medical": ["Source Sans Pro", "system-ui", "sans-serif"]
                }
            }
        }
    }
    
    # Deployment configuration
    deploy_url = "https://psychiatry-emr.com"
    
    # Security headers for production
    cors_allowed_origins = [
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "https://psychiatry-emr.com"
    ]
    
    # Performance optimization
    compile = True
    
    # Logging configuration
    loglevel = "INFO" if not getattr(settings, 'debug_mode', False) else "DEBUG"

# Create config instance
config = PsychiatryEMRConfig(
    app_name="labap1py"
)
