#!/bin/bash
# Production deployment script
set -e

echo "🚀 Deploying Psychiatry EMR to Production"

# Verify environment
if [ -z "$ENCRYPTION_SALT" ]; then
    echo "❌ ENCRYPTION_SALT not set"
    exit 1
fi

if [ -z "$SECRET_KEY" ]; then
    echo "❌ SECRET_KEY not set"
    exit 1
fi

if [ -z "$DATABASE_URL" ]; then
    echo "❌ DATABASE_URL not set"
    exit 1
fi

# Create backup directory
mkdir -p backups

# Database backup before deployment
echo "📁 Creating database backup..."
BACKUP_FILE="backups/backup_$(date +%Y%m%d_%H%M%S).sql"
pg_dump $DATABASE_URL > $BACKUP_FILE
echo "✅ Backup created: $BACKUP_FILE"

# Run security tests
echo "🔒 Running security tests..."
python -m pytest tests/security/ -v

# Run integration tests
echo "🧪 Running integration tests..."
python -m pytest tests/integration/ -v

# Database migrations
echo "🗄️ Running database migrations..."
alembic upgrade head

# Build and deploy application
echo "📦 Building and deploying application..."
docker-compose -f docker-compose.prod.yml build
docker-compose -f docker-compose.prod.yml up -d

# Wait for services to start
echo "⏳ Waiting for services to start..."
sleep 30

# Health check
echo "🏥 Performing health check..."
for i in {1..10}; do
    if curl -f http://localhost:3000/health > /dev/null 2>&1; then
        echo "✅ Health check passed"
        break
    fi
    
    if [ $i -eq 10 ]; then
        echo "❌ Health check failed after 10 attempts"
        docker-compose -f docker-compose.prod.yml logs app
        exit 1
    fi
    
    echo "⏳ Health check attempt $i failed, retrying..."
    sleep 10
done

echo "✅ Deployment complete!"
echo "📊 Application is running at http://localhost:3000"
echo "🗄️ Database backup saved to: $BACKUP_FILE"
