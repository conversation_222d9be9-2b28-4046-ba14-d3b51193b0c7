# Psychiatry Patient Management System - Project Overview

## Project Overview
A secure, local psychiatry EMR application built with Python Reflex, implementing DSM-5-TR criteria with intelligent forms, comprehensive patient management, and robust security features.

## Architecture & Technology Stack

### Core Framework
- **Frontend/Backend**: Reflex (Python-based full-stack framework)
- **Database**: Local PostgreSQL with SQLModel ORM (Reflex's built-in)
- **Authentication**: Reflex built-in authentication with @rx.require_auth decorators
- **Styling**: Built-in Reflex components and theming
- **Security**: Explicit encryption in service layer + PostgreSQL pgcrypto as backup
- **Validation**: Pydantic for all data contracts and state management

### Key Libraries
```python
# Streamlined Dependencies
reflex>=0.4.0
sqlmodel  # Built into Reflex, wraps SQLAlchemy cleanly
psycopg2-binary  # Local PostgreSQL connection
pandas  # Data analysis
python-dotenv  # Environment variables
pydantic>=2.0  # Strong data validation
cryptography  # Explicit encryption/decryption
pyyaml  # DSM-5-TR criteria configuration
fuzzywuzzy  # Duplicate patient detection
alembic  # Database migrations

# Testing
pytest
pytest-postgresql
pytest-benchmark
pytest-cov
pytest-html
playwright
black
ruff
pre-commit
mypy
```

## Project Structure
```
psychiatry_emr/
├── main.py                     # Application entry point
├── requirements.txt            # Production dependencies
├── requirements-dev.txt        # Development dependencies
├── .env                        # Environment configuration (create from template)
├── .pre-commit-config.yaml     # Code quality hooks
├── alembic/                    # Database migrations
│   ├── env.py
│   └── versions/
├── config/
│   ├── settings.py             # Application configuration
│   └── dsm5_criteria.yaml     # DSM-5-TR diagnostic criteria
├── models/
│   ├── __init__.py
│   ├── patient.py              # Patient data models
│   ├── clinical.py             # Clinical assessment models
│   └── audit.py                # Audit logging models
├── security/
│   └── encryption.py           # Encryption service implementation
├── services/
│   ├── database.py             # Database connection management
│   ├── patient_service.py      # Patient business logic
│   ├── dsm5_engine.py          # DSM-5-TR rule engine
│   └── audit_service.py        # Audit logging service
├── states/
│   └── patient_state.py        # Reflex state management
├── pages/
│   ├── __init__.py
│   ├── dashboard.py            # Main dashboard
│   ├── patient_search.py       # Patient search interface
│   └── clinical_assessment.py  # DSM-5-TR assessment forms
├── components/
│   ├── __init__.py
│   ├── patient_form.py         # Patient data entry forms
│   └── dsm5_forms.py           # DSM-5-TR diagnostic forms
├── sql/
│   ├── setup_security.sql      # Database security configuration
│   └── performance_indexes.sql # Performance optimization indexes
└── tests/
    ├── conftest.py             # Test configuration
    ├── security/               # Security-focused tests
    ├── integration/            # Integration tests
    ├── services/               # Service layer tests
    └── performance/            # Performance benchmarks
```

## Key Features

### Security & Privacy
- **Multi-layer encryption**: Application-level encryption with database backup
- **Master password authentication**: Required on startup, not stored
- **Comprehensive audit trail**: All actions logged with user attribution
- **Role-based access control**: Granular patient access permissions
- **HIPAA compliance considerations**: Built for healthcare privacy requirements

### Clinical Features
- **DSM-5-TR integration**: Configurable diagnostic criteria engine
- **Intelligent forms**: Dynamic forms based on diagnostic criteria
- **Patient management**: Comprehensive patient records with duplicate detection
- **Clinical assessments**: Structured present illness documentation
- **Search and reporting**: Encrypted search with performance optimization

### Development Features
- **Type safety**: Pydantic models throughout for validation
- **Comprehensive testing**: Security, integration, and performance tests
- **Code quality**: Pre-commit hooks with Black, Ruff, and MyPy
- **Database migrations**: Alembic integration for schema changes
- **Development workflow**: Automated setup and testing scripts

## Next Steps

1. **Environment Setup**: Review and customize configuration files
2. **Database Configuration**: Set up PostgreSQL with security extensions
3. **Security Implementation**: Generate encryption keys and configure master password
4. **Development Workflow**: Install dependencies and run development server
5. **Testing Strategy**: Execute security and integration tests
6. **Production Deployment**: Follow security checklist and deployment guide

## File Organization

The remaining files in this blueprint contain:
- **02_security_encryption.md**: Security architecture and encryption implementation
- **03_data_models_services.md**: Database models and business logic services
- **04_dsm5_state_management.md**: DSM-5-TR engine and UI state management
- **05_database_configuration.md**: Database setup, migrations, and performance
- **06_testing_deployment.md**: Testing strategy and production deployment
- **07_development_setup.md**: Development environment and workflow setup