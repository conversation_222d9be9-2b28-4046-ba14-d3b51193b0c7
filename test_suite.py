#!/usr/bin/env python3
"""
Psychiatry EMR - Comprehensive Test Suite
Production-ready test suite for all application components.
"""

import os
import sys
import unittest
from datetime import datetime
from sqlmodel import Session, SQLModel

# Add project root to path
sys.path.append('.')

# Set test environment variables
os.environ['MASTER_PASSWORD'] = 'test_password_123'
os.environ['ENCRYPTION_SALT'] = 'a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456'

class TestEncryptionService(unittest.TestCase):
    """Test encryption service functionality"""
    
    def setUp(self):
        from security.encryption import initialize_encryption
        self.encryption_service = initialize_encryption('test_password_123')
    
    def test_encryption_decryption(self):
        """Test basic encryption and decryption"""
        test_data = "Sensitive patient information"
        encrypted = self.encryption_service.encrypt(test_data)
        decrypted = self.encryption_service.decrypt(encrypted)
        self.assertEqual(test_data, decrypted)
    
    def test_empty_data_encryption(self):
        """Test encryption of empty data"""
        encrypted = self.encryption_service.encrypt("")
        decrypted = self.encryption_service.decrypt(encrypted)
        self.assertEqual("", decrypted)

class TestDatabaseConnection(unittest.TestCase):
    """Test database connectivity and basic operations"""
    
    def setUp(self):
        from services.database import get_engine
        self.engine = get_engine()
    
    def test_database_connection(self):
        """Test database connection"""
        from sqlmodel import text
        with self.engine.connect() as conn:
            result = conn.execute(text("SELECT 1 as test"))
            row = result.fetchone()
            self.assertEqual(row[0], 1)

class TestDSM5Engine(unittest.TestCase):
    """Test DSM-5 rule engine functionality"""
    
    def setUp(self):
        from services.dsm5_engine import DSM5RuleEngine
        self.dsm5_engine = DSM5RuleEngine()
    
    def test_engine_initialization(self):
        """Test DSM-5 engine initialization"""
        self.assertIsNotNone(self.dsm5_engine)
    
    def test_available_disorders(self):
        """Test getting available disorders"""
        disorders = self.dsm5_engine.list_available_disorders()
        self.assertGreater(len(disorders), 0)
        self.assertIn('code', disorders[0])
        self.assertIn('name', disorders[0])
    
    def test_disorder_criteria(self):
        """Test getting disorder criteria"""
        disorders = self.dsm5_engine.list_available_disorders()
        if disorders:
            first_disorder = disorders[0]
            criteria = self.dsm5_engine.get_disorder_criteria(first_disorder['code'])
            self.assertIsNotNone(criteria)
            self.assertGreater(len(criteria.criteria), 0)

class TestAuthenticationService(unittest.TestCase):
    """Test authentication service functionality"""
    
    def setUp(self):
        from services.database import get_engine
        from auth.auth_service import AuthService
        from sqlmodel import SQLModel
        
        self.engine = get_engine()
        SQLModel.metadata.create_all(self.engine)
        
        with Session(self.engine) as session:
            self.auth_service = AuthService(session)
            
            # Create test user if not exists
            from models.user import User
            from sqlmodel import select
            existing_user = session.exec(
                select(User).where(User.username == 'test_user')
            ).first()
            
            if not existing_user:
                test_user = self.auth_service.create_user(
                    username='test_user',
                    password='test_password',
                    full_name='Test User',
                    role='clinician'
                )
                session.commit()
    
    def test_user_authentication(self):
        """Test user authentication flow"""
        from auth.auth_service import AuthService

        with Session(self.engine) as session:
            auth_service = AuthService(session)
            
            # Test authentication
            auth_result = auth_service.authenticate_user(
                'test_user', 'test_password', '127.0.0.1', 'Test Agent'
            )
            
            self.assertIsNotNone(auth_result)
            self.assertEqual(auth_result['username'], 'test_user')
            self.assertIn('session_token', auth_result)
            
            # Test session validation
            session_info = auth_service.validate_session(auth_result['session_token'])
            self.assertIsNotNone(session_info)
            self.assertEqual(session_info['username'], 'test_user')
            
            # Test logout
            logout_result = auth_service.logout_user(auth_result['session_token'])
            self.assertTrue(logout_result)

class TestPatientService(unittest.TestCase):
    """Test patient service functionality"""
    
    def setUp(self):
        from services.database import get_engine
        from services.patient_service import PatientService
        from security.encryption import initialize_encryption
        from sqlmodel import SQLModel
        
        self.engine = get_engine()
        SQLModel.metadata.create_all(self.engine)
        self.encryption_service = initialize_encryption('test_password_123')
        self.current_user_id = 1  # Admin user
    
    def test_patient_search(self):
        """Test patient search functionality"""
        from services.patient_service import PatientService

        with Session(self.engine) as session:
            patient_service = PatientService(session, self.current_user_id, self.encryption_service)
            
            # Test search (should not crash even with no patients)
            search_results = patient_service.search_patients('test')
            self.assertIsInstance(search_results, list)

def run_all_tests():
    """Run all tests and return results"""
    print("🧪 Running Psychiatry EMR Test Suite...")
    print("=" * 60)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestEncryptionService,
        TestDatabaseConnection,
        TestDSM5Engine,
        TestAuthenticationService,
        TestPatientService
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    print("\n" + "=" * 60)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFailures:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\nErrors:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    print(f"\n{'✅ ALL TESTS PASSED!' if success else '❌ SOME TESTS FAILED!'}")
    
    return success

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
